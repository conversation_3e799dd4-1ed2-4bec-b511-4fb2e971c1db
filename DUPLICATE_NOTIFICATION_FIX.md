# Duplicate Notification Fix for iOS

## Problem Description

On some older iOS versions (particularly iOS 14+), FCM notifications are sometimes displayed automatically in the foreground, even though the documentation states they should only be shown manually. This causes duplicate notifications:

1. **Automatic notification** - iOS/FCM shows the notification automatically
2. **Manual notification** - Your app shows it manually via `displayNotification()`

Result: Users see the same notification twice.

## Root Cause

- **iOS Behavior Inconsistency**: Older iOS versions with certain notification settings may auto-display FCM notifications in foreground
- **FCM Documentation Gap**: The behavior isn't consistent across all iOS versions and configurations
- **No Built-in Detection**: No direct API to detect if iOS has already shown a notification

## Solution Implemented

### 1. Duplicate Detection System

**Key Components:**
- **Notification Key Generation**: Creates unique keys based on content and timestamp
- **Recent Notifications Tracking**: Maintains a Set of recently processed notifications
- **iOS Version Detection**: Identifies iOS version for behavior prediction
- **Auto-Display Detection**: Checks if iOS has already shown the notification

### 2. Smart Foreground Display Logic

```typescript
const shouldShowManualNotification = async (remoteMessage: any): Promise<boolean> => {
  // Android always needs manual display
  if (Platform.OS !== 'ios') return true;
  
  // Check for duplicates using notification key
  const notificationKey = generateNotificationKey(remoteMessage);
  if (recentNotifications.has(notificationKey)) {
    return false; // Skip duplicate
  }
  
  // For iOS 14+, wait and check if iOS auto-displayed
  if (iosVersion >= 14) {
    // Wait 500ms then check displayed notifications
    const displayedNotifications = await notifee.getDisplayedNotifications();
    const hasMatchingNotification = displayedNotifications.some(/* match logic */);
    return !hasMatchingNotification; // Don't show if already displayed
  }
  
  return true; // Default to showing
};
```

### 3. Enhanced Notification Flow

**Before (Problematic):**
```
FCM Message → Always Display Manual Notification → Potential Duplicate
```

**After (Fixed):**
```
FCM Message → Check for Duplicates → Check iOS Auto-Display → Conditionally Display
```

## Files Modified

### 1. `src/services/NotificationService.ts`
- Added `generateNotificationKey()` function
- Added `shouldShowManualNotification()` function  
- Enhanced `displayRemoteMessage()` with duplicate detection
- Added iOS version detection
- Added recent notifications tracking

### 2. `src/hooks/useNotifications.ts`
- Exported duplicate detection functions
- Added debug utilities to hook API

### 3. `src/utils/notificationDebug.ts`
- Added `testDuplicateDetection()` function
- Added `monitorForegroundNotifications()` function
- Enhanced debugging capabilities

## How It Works

### Step 1: Notification Key Generation
```typescript
const generateNotificationKey = (remoteMessage: any): string => {
  const title = remoteMessage.notification?.title || '';
  const body = remoteMessage.notification?.body || '';
  const messageId = remoteMessage.messageId || '';
  
  // Round timestamp to 5-second intervals to catch near-duplicates
  const timestamp = Math.floor(Date.now() / 5000) * 5000;
  return `${title}_${body}_${messageId}_${timestamp}`;
};
```

### Step 2: Duplicate Detection
- Maintains a `Set<string>` of recent notification keys
- Keys expire after 10 seconds automatically
- Prevents showing notifications with identical content within the time window

### Step 3: iOS Auto-Display Detection
- For iOS 14+, waits 500ms after receiving FCM message
- Checks `notifee.getDisplayedNotifications()` for matching notifications
- If found, skips manual display to prevent duplicate

### Step 4: Fallback Safety
- If detection fails, defaults to showing the notification
- Better to show a duplicate than miss a notification entirely
- Comprehensive error handling prevents crashes

## Testing the Fix

### 1. Manual Testing
```typescript
import { testDuplicateDetection } from '../utils/notificationDebug';

// Test duplicate detection logic
await testDuplicateDetection();
```

### 2. Real-time Monitoring
```typescript
import { monitorForegroundNotifications } from '../utils/notificationDebug';

// Start monitoring (returns cleanup function)
const stopMonitoring = monitorForegroundNotifications();

// Stop monitoring when done
stopMonitoring();
```

### 3. Console Log Monitoring
Look for these log patterns:

**Duplicate Detected:**
```
🔄 Duplicate notification detected, skipping manual display
```

**iOS Auto-Display Detected:**
```
📱 iOS auto-displayed notification detected, skipping manual display
```

**Manual Display Proceeding:**
```
📱 Displaying manual foreground notification
```

## Configuration Options

### Timing Adjustments
```typescript
// Adjust duplicate detection window (currently 5 seconds)
const timestamp = Math.floor(Date.now() / 5000) * 5000;

// Adjust iOS auto-display check delay (currently 500ms)
setTimeout(async () => { /* check logic */ }, 500);

// Adjust cleanup timeout (currently 10 seconds)
setTimeout(() => {
  recentNotifications.delete(notificationKey);
}, 10000);
```

### iOS Version Targeting
```typescript
// Currently targets iOS 14+, can be adjusted
if (iosVersion >= 14) {
  // Enhanced duplicate detection
}
```

## Monitoring and Maintenance

### 1. Enable Debug Logging
- Monitor console logs for duplicate detection events
- Track notification display patterns
- Identify iOS versions with issues

### 2. Performance Monitoring
- `recentNotifications` Set automatically cleans up
- No memory leaks from notification tracking
- Minimal performance impact

### 3. User Feedback
- Monitor user reports of duplicate notifications
- Adjust timing parameters if needed
- Consider iOS version-specific tweaks

## Future Enhancements

### 1. Adaptive Timing
- Adjust detection timing based on device performance
- Learn from user's notification patterns

### 2. Enhanced iOS Detection
- More sophisticated iOS behavior detection
- Device-specific notification settings consideration

### 3. Analytics Integration
- Track duplicate detection rates
- Monitor effectiveness across iOS versions
- Identify problematic notification types

## Troubleshooting

### Issue: Still seeing duplicates
**Solution:** 
- Check iOS version detection: `console.log('iOS Version:', iosVersion)`
- Verify notification key generation: Use `generateNotificationKey()` debug function
- Monitor timing: Increase auto-display check delay

### Issue: Missing notifications
**Solution:**
- Check error logs for detection failures
- Verify fallback logic is working
- Ensure `shouldShowManualNotification()` defaults to `true`

### Issue: Performance problems
**Solution:**
- Monitor `recentNotifications` Set size
- Verify cleanup timeouts are working
- Check for memory leaks in notification tracking

## Compatibility

- **iOS 10+**: Basic duplicate detection
- **iOS 14+**: Enhanced auto-display detection  
- **Android**: No changes (always shows manual notifications)
- **React Native Firebase**: Compatible with all versions
- **Notifee**: Requires version with `getDisplayedNotifications()` support
