import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import ChainedBackend from 'i18next-chained-backend';
import HttpApi from 'i18next-http-backend';
import AsyncStorageBackend from 'i18next-async-storage-backend2';
import Config from 'react-native-config';
import RNLanguageDetector from './language-detector';

const backendOptions = {
	// loadPath: `${Config.LANGUAGE_JSON_S3_URL}/tab/{{lng}}.json`,
	loadPath: (lng) => {
		const url = `${Config.LANGUAGE_JSON_S3_URL}/tab/${lng}.json`;
		// console.log('URL ', url);
		return url;
	},
	allowMultiLoading: false,
	crossDomain: true
	// request: async (_options: any, url: string, _payload: any, callback: any) => {
	// 	if (url) {
	// 		const res = await axios.get(url);
	// 		const data = await res.data;
	// 		const callbackData = { data, status: 200 };
	// 		callback(null, callbackData);
	// 	}
	// }
};

i18n
	.use(ChainedBackend)
	.use(initReactI18next)
	.use(RNLanguageDetector)
	.init({
		backend: {
			backends: [
				AsyncStorageBackend, // primary
				HttpApi // fallback
			],
			backendOptions: [
				{
					// prefix for stored languages
					prefix: 'i18next_res_',

					// expiration
					expirationTime: 0.5 * 24 * 60 * 60 * 1000,
					// language versions
					versions: {}
				},
				{
					...backendOptions
				}
			]
		},
		compatibilityJSON: 'v3',
		// resources,
		fallbackLng: 'en', //language to use if translations in user language are not available
		interpolation: {
			escapeValue: false // not needed for react!!
		},
		// supportedLngs: ['en', 'ar'],
		react: {
			useSuspense: false
		}
	});

export default i18n;
