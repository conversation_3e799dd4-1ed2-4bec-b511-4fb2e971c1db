/**
 * NotificationService.ts
 *
 * A functional service for handling all notification-related functionality
 * including FCM token management, notification display, scheduled notifications,
 * and handling notification interactions.
 */

import { Platform } from 'react-native';
import messaging from '@react-native-firebase/messaging';
import notifee, {
	AndroidImportance,
	AndroidStyle,
	EventType,
	IOSNotificationAttachment,
	AuthorizationStatus,
	TimestampTrigger,
	TriggerType,
	RepeatFrequency
} from '@notifee/react-native';
import DeviceInfo from 'react-native-device-info';
import { addMinutes, getHours, getMinutes, subMinutes } from 'date-fns';
import i18n from '../locales/i18n';
import { store } from '../redux/store';
import { addUnreadCount } from '../redux/features/notification-slice';
import { setMemebrScanReward } from '../redux/features/reward-slice';
import { isSameOrBefore } from '../utils/functions';

// Type definitions
// type NotificationHandlerCallback = (data: any) => void;
type NotificationConfig = {
	title: string;
	body: string;
	data?: any;
	imageUrl?: string;
	channelId?: string;
};

// Reminder notification options
type ReminderOptions = {
	shiftName: string;
	shiftTime: string;
	clockMinutes: number;
	clockType: 'clock_in' | 'clock_out';
	notificationId: string;
};

// Module state (private)
let deviceName = '';
let listeners: Array<() => void> = [];
let isInitialized = false;

// Initialize device name
DeviceInfo.getDeviceName().then(name => {
	deviceName = name;
});

/**
 * Create all required notification channels for Android
 */
const createNotificationChannels = async (): Promise<void> => {
	if (Platform.OS === 'android') {
		// Create default channel for regular notifications with maximum priority
		await notifee.createChannel({
			id: 'default',
			name: 'Default Notifications',
			importance: AndroidImportance.HIGH,
			sound: 'default',
			vibrationPattern: [300, 500],
			lights: true,
			lightColor: '#FF0000',
			// Ensure notifications show on lock screen and as heads-up
			visibility: 1, // VISIBILITY_PUBLIC
			bypassDnd: true // Bypass Do Not Disturb
		});

		// Create a separate channel for foreground service
		await notifee.createChannel({
			id: 'foreground_service',
			name: 'Foreground Service',
			importance: AndroidImportance.HIGH,
			sound: 'default',
			vibrationPattern: [300, 500],
			lights: true,
			lightColor: '#FF0000',
			visibility: 1,
			bypassDnd: true
		});

		// Create a channel for scheduled notifications
		await notifee.createChannel({
			id: 'scheduled',
			name: 'Scheduled Notifications',
			importance: AndroidImportance.HIGH,
			sound: 'default',
			vibrationPattern: [300, 500],
			lights: true,
			lightColor: '#FF0000',
			visibility: 1,
			bypassDnd: true
		});
	}
};

/**
 * Synchronize badge count between Redux state and system badge
 */
const syncBadgeCount = async (): Promise<void> => {
	try {
		const state = store.getState();
		const unreadCount = state.notification.unreadCount || 0;
		await notifee.setBadgeCount(unreadCount);
		console.log(`Badge count synchronized: ${unreadCount}`);
	} catch (error) {
		console.error('Error synchronizing badge count:', error);
	}
};

/**
 * Clear badge count completely
 */
const clearBadgeCount = async (): Promise<void> => {
	try {
		await notifee.setBadgeCount(0);
		console.log('Badge count cleared');
	} catch (error) {
		console.error('Error clearing badge count:', error);
	}
};

/**
 * Initialize the notification service
 */
const initialize = async (): Promise<void> => {
	if (!isInitialized) {
		// Create notification channels on Android
		await createNotificationChannels();

		// Clear badge count on fresh app launch to prevent stale badges
		await clearBadgeCount();

		// Check for initial notification that might have opened the app
		const initialNotification = await notifee.getInitialNotification();
		if (initialNotification) {
			console.log('App was opened by a notification', initialNotification);
		}

		isInitialized = true;
	}
};

/**
 * Request notification permissions from the user
 * This handles permissions for all notification types:
 * - FCM notifications (foreground and background)
 * - Scheduled/timer notifications
 * - Foreground service notifications
 */
const requestPermissions = async (): Promise<boolean> => {
	try {
		// Request permission from Notifee (handles local, scheduled, and foreground service notifications)
		const notifeeSettings = await notifee.requestPermission({
			// For iOS
			alert: true,
			badge: true,
			sound: true,
			// For Android, these are handled by the channel settings
			// but we include them here for completeness
			criticalAlert: true,
			provisional: true
		});

		// Request permission from FCM (handles remote notifications)
		const messagingAuthStatus = await messaging().requestPermission({
			// Request full permission including alert, badge, sound
			provisional: true
		});

		// Check if permissions were granted
		const enabled =
			notifeeSettings.authorizationStatus >= AuthorizationStatus.AUTHORIZED ||
			messagingAuthStatus === messaging.AuthorizationStatus.AUTHORIZED ||
			messagingAuthStatus === messaging.AuthorizationStatus.PROVISIONAL;

		// Log permission status
		console.log('Notification permission status:', {
			notifee: notifeeSettings.authorizationStatus,
			fcm: messagingAuthStatus,
			enabled
		});

		// On Android, we need to create the notification channels
		if (Platform.OS === 'android' && enabled) {
			await createNotificationChannels();
		}

		return enabled;
	} catch (error) {
		console.error('Error requesting notification permissions:', error);
		return false;
	}
};

/**
 * Get the FCM token for the device
 */
const getFCMToken = async (): Promise<string | null> => {
	try {
		const token = await messaging().getToken();
		if (token) {
			console.log(`${deviceName}-FCM Token:`, token);
			return token;
		}
		return null;
	} catch (error) {
		console.error('Error getting FCM token:', error);
		return null;
	}
};

/**
 * Display a local notification
 */
const displayNotification = async (config: NotificationConfig): Promise<void> => {
	try {
		const { title, body, data, imageUrl, channelId = 'default' } = config;

		// Generate a consistent notification ID based on data
		// This helps prevent duplicate notifications
		let notificationId: string;

		if (data?.notificationId) {
			// Use provided ID if available
			notificationId = String(data.notificationId);
		} else if (data?._id) {
			// Use data ID if available (for content-specific notifications)
			notificationId = `notification_${data._id}`;
		} else if (data?.type) {
			// Use type and timestamp for type-specific notifications
			notificationId = `${data.type}_${Date.now()}`;
		} else {
			// Fallback to timestamp
			notificationId = Date.now().toString();
		}

		// Prepare iOS attachments if image is provided
		const iosAttachments: IOSNotificationAttachment[] = imageUrl
			? [{ url: imageUrl }]
			: [];

		// Create notification config with high priority settings
		const notificationConfig: any = {
			id: notificationId,
			title,
			body,
			data: {
				...data,
				notificationId // Ensure notificationId is included in data for later reference
			},
			android: {
				channelId,
				smallIcon: 'ic_small',
				importance: AndroidImportance.HIGH,
				pressAction: {
					id: 'default',
					launchActivity: 'default'
				},
				// Ensure high priority display
				visibility: 1, // VISIBILITY_PUBLIC
				showTimestamp: true
			},
			ios: {
				// High priority settings for iOS
				sound: 'default'
				// Note: critical and interruptionLevel require special entitlements
				// For standard high priority, we rely on the notification content and timing
			}
		};

		// Add image styling if available
		if (imageUrl) {
			notificationConfig.android.largeIcon = imageUrl;
			notificationConfig.android.style = {
				type: AndroidStyle.BIGPICTURE,
				picture: imageUrl
			};
			notificationConfig.ios.attachments = iosAttachments;
		}

		// Handle badge count properly
		if (data?.badge) {
			const badgeCount = parseInt(data.badge, 10);
			await notifee.setBadgeCount(badgeCount);
			// Also set badge for iOS specifically
			if (Platform.OS === 'ios') {
				notificationConfig.ios.badgeCount = badgeCount;
			}
		} else {
			// Sync with current Redux state if no badge provided
			await syncBadgeCount();
		}

		// Cancel any existing notification with the same ID to prevent duplicates
		await notifee.cancelNotification(notificationId);

		// Display the notification
		await notifee.displayNotification(notificationConfig);

		console.log(`High priority notification displayed: ${title}`);
	} catch (error) {
		console.error('Error displaying notification:', error);
	}
};

/**
 * Process notification data for special actions
 */
const processNotificationData = async (data: any): Promise<void> => {
	if (!data) return;

	// Update notification count and sync badge
	if (data.badge) {
		store.dispatch(addUnreadCount(data.badge));
		// Sync badge count after updating Redux state
		await syncBadgeCount();
	}

	// Handle specific notification types
	switch (data.type) {
		case 'REWARD_MEMBER_SCAN':
			store.dispatch(setMemebrScanReward({ data }));
			break;
		// Add more cases as needed
	}
};

/**
 * Display a remote message as a local notification
 * Only displays notifications in foreground mode
 */
const displayRemoteMessage = async (remoteMessage: any, isAppInForeground: boolean = false): Promise<void> => {
	if (!remoteMessage.data) return;

	// Only display manual notifications in foreground mode
	// Firebase handles background and quit state notifications automatically
	if (!isAppInForeground) {
		console.log('Skipping manual notification display in background/quit mode');
		return;
	}

	const imageUrl =
		remoteMessage.data?.android?.imageUrl ||
		remoteMessage.data?.image ||
		remoteMessage.data?.fcm_options?.image;

	await displayNotification({
		title: remoteMessage.notification?.title
			? remoteMessage.notification?.title
			: remoteMessage.data?.title || 'New Notification',
		body: remoteMessage.notification?.body
			? remoteMessage.notification?.body
			: remoteMessage.data?.message || 'No message body',
		data: remoteMessage.data,
		imageUrl
	});
};

/**
 * Remove all notification listeners
 */
const removeAllListeners = (): void => {
	listeners.forEach(removeListener => {
		if (typeof removeListener === 'function') {
			removeListener();
		}
	});
	listeners = [];
};

/**
 * Initialize notification listeners
 */
const initializeListeners = (navigationCallback: (data: any) => void): void => {
	// Ensure the service is initialized
	initialize();

	// Clear any existing listeners
	removeAllListeners();

	// Handle foreground messages
	const onMessageListener = messaging().onMessage(async (remoteMessage) => {
		console.log('Foreground Notification:', deviceName, remoteMessage);

		// Process notification data
		await processNotificationData(remoteMessage.data);

		// Display the notification only in foreground mode
		await displayRemoteMessage(remoteMessage, true);
	});
	listeners.push(onMessageListener);

	// Handle when app is opened from a background notification
	const onNotificationOpenedListener = messaging().onNotificationOpenedApp((remoteMessage) => {
		console.log('Opened from Background:', deviceName, remoteMessage);

		// Clear the notification when opened
		if (remoteMessage.data?.notificationId) {
			const notificationId = String(remoteMessage.data.notificationId);
			notifee.cancelNotification(notificationId);
		}

		navigationCallback(remoteMessage.data);
	});
	listeners.push(onNotificationOpenedListener);

	// Handle foreground notification press
	const foregroundEventListener = notifee.onForegroundEvent(({ type, detail }) => {
		if (type === EventType.PRESS) {
			console.log('Foreground Notification Pressed:', detail.notification?.data);

			// Clear the notification when pressed
			if (detail.notification?.id) {
				notifee.cancelNotification(detail.notification.id);
			}

			navigationCallback(detail.notification?.data);
		} else if (type === EventType.DISMISSED) {
			// Handle notification dismissal (swipe)
			console.log('Notification dismissed:', detail.notification?.id);
			// No need to cancel as it's already dismissed
		}
	});
	listeners.push(foregroundEventListener);

	// Handle background notification press
	notifee.onBackgroundEvent(async ({ type, detail }) => {
		if (type === EventType.PRESS) {
			console.log('Background Notification Pressed:', detail.notification?.data);

			// Clear the notification when pressed
			if (detail.notification?.id) {
				await notifee.cancelNotification(detail.notification.id);
			}

			// This will be handled by the app when it comes to the foreground
		} else if (type === EventType.DISMISSED) {
			// Handle notification dismissal (swipe)
			console.log('Background notification dismissed:', detail.notification?.id);
			// No need to cancel as it's already dismissed
		}
	});

	// Set background message handler
	messaging().setBackgroundMessageHandler(async (remoteMessage) => {
		console.log('Background Notification:', deviceName, remoteMessage);

		// No need to manually display notifications in background mode
		// Firebase will handle this automatically
		// Process data only and sync badge
		await processNotificationData(remoteMessage.data);
	});

	console.log('✅ Notification listeners registered');
};

/**
 * Clear all notifications and reset badge count
 */
const clearAllNotifications = async (): Promise<void> => {
	try {
		await notifee.cancelAllNotifications();
		await clearBadgeCount();
		console.log('All notifications cleared and badge removed');
	} catch (error) {
		console.error('Error clearing notifications or badge:', error);
	}
};

/**
 * Clear all notifications and badge count on logout
 * This ensures no stale notifications or badges remain after logout
 */
const clearNotificationsOnLogout = async (): Promise<void> => {
	try {
		// Cancel all notifications
		await notifee.cancelAllNotifications();

		// Cancel all scheduled/trigger notifications
		await notifee.cancelTriggerNotifications();

		// Clear badge count
		await clearBadgeCount();

		// Stop foreground service if running
		if (Platform.OS === 'android') {
			try {
				await notifee.stopForegroundService();
			} catch {
				// Ignore error if no foreground service is running
				console.log('No foreground service to stop');
			}
		}

		console.log('All notifications and badges cleared on logout');
	} catch (error) {
		console.error('Error clearing notifications on logout:', error);
	}
};

/**
 * Check for initial notification that launched the app
 */
const checkInitialNotification = async (navigationCallback: (data: any) => void): Promise<void> => {
	try {
		// Check for initial notification from notifee
		const initialNotifee = await notifee.getInitialNotification();
		if (initialNotifee) {
			console.log('App opened by notifee notification:', initialNotifee.notification);

			// Clear the notification when app is opened from it
			if (initialNotifee.notification?.id) {
				await notifee.cancelNotification(initialNotifee.notification.id);
			}

			navigationCallback(initialNotifee.notification.data);
			return;
		}

		// Check for initial notification from FCM
		const initialFCM = await messaging().getInitialNotification();
		if (initialFCM) {
			console.log('App opened by FCM notification:', initialFCM);

			// Clear the notification when app is opened from it
			if (initialFCM.data?.notificationId) {
				const notificationId = String(initialFCM.data.notificationId);
				await notifee.cancelNotification(notificationId);
			}

			navigationCallback(initialFCM.data);
		}
	} catch (error) {
		console.error('Error checking initial notification:', error);
	}
};

/**
 * Helper function to split time string into hours and minutes
 */
const getSplitTime = (time: string) => {
	const data = time.split(':');
	return {
		hours: Number(data[0]),
		minutes: Number(data[1])
	};
};

/**
 * Create a scheduled reminder notification
 */
const createReminderNotification = async (options: ReminderOptions): Promise<void> => {
	try {
		const { clockType, clockMinutes, shiftTime, notificationId, shiftName } = options;

		const data = getSplitTime(shiftTime);
		const date = new Date();
		date.setHours(data.hours, data.minutes, 0);
		const newDate = clockType === 'clock_in' ? subMinutes(date, clockMinutes) : addMinutes(date, clockMinutes);
		const hours = getHours(newDate);
		const minutes = getMinutes(newDate);
		date.setHours(hours, minutes, 0);

		// To fix "date must be in the future" error in notifee.createTriggerNotification
		if (isSameOrBefore(date, new Date())) {
			date.setDate(date.getDate() + 1);
		}

		// Create a time-based trigger
		const trigger: TimestampTrigger = {
			type: TriggerType.TIMESTAMP,
			timestamp: date.getTime(),
			repeatFrequency: RepeatFrequency.DAILY
		};

		const clockInMsg = i18n.t('notification_clockIn', { clockMinutes: clockMinutes });
		const clockOutMsg = i18n.t('notification_clockOut');

		// Ensure the channel exists
		await createNotificationChannels();

		// Log notification creation with detailed info
		console.log(`📅 Creating ${clockType} reminder notification:`, {
			notificationId,
			shiftName,
			shiftTime,
			clockMinutes,
			scheduledFor: date.toISOString(),
			timestamp: date.getTime()
		});

		// Create a trigger notification
		await notifee.createTriggerNotification(
			{
				id: notificationId,
				title: i18n.t('visit_notification_title', { clockType: i18n.t(clockType) }),
				body: clockType === 'clock_in' ? clockInMsg : clockOutMsg,
				android: {
					channelId: 'scheduled',
					importance: AndroidImportance.HIGH,
					sound: 'default',
					visibility: 1,
					showTimestamp: true
				},
				ios: {
					sound: 'default'
				}
			},
			trigger
		);

		console.log(`✅ Successfully created ${clockType} reminder notification with ID: ${notificationId}`);
	} catch (error: any) {
		console.error(`❌ createReminderNotification error for ${options.clockType}:`, error.message);
	}
};

/**
 * Debug function to list all scheduled notifications
 */
const listScheduledNotifications = async (): Promise<void> => {
	try {
		const triggerNotifications = await notifee.getTriggerNotifications();
		console.log('📋 Currently scheduled notifications:', triggerNotifications.length);

		triggerNotifications.forEach((notification, index) => {
			const trigger = notification.trigger as TimestampTrigger;
			const scheduledDate = new Date(trigger.timestamp);
			console.log(`${index + 1}. ID: ${notification.notification.id}, Title: ${notification.notification.title}, Scheduled: ${scheduledDate.toLocaleString()}`);
		});
	} catch (error) {
		console.error('❌ Error listing scheduled notifications:', error);
	}
};

/**
 * Remove all scheduled reminder notifications
 */
const removeAllReminderNotifications = async (): Promise<void> => {
	console.log('🗑️ Removing all reminder notifications');

	// List current notifications before removal for debugging
	await listScheduledNotifications();

	await notifee.cancelTriggerNotifications();
	console.log('✅ All reminder notifications removed');
};

/**
 * Remove clock-in notifications
 * Handles dynamic IDs generated by the pattern 100{index+1}
 */
const removeClockInNotifications = async (): Promise<void> => {
	try {
		console.log('🗑️ Removing clock-in notifications');

		// Get all trigger notifications to find clock-in ones
		const triggerNotifications = await notifee.getTriggerNotifications();
		const clockInIds = triggerNotifications
			.filter(notification => notification.notification.id?.startsWith('100'))
			.map(notification => notification.notification.id!);

		if (clockInIds.length > 0) {
			console.log('📋 Found clock-in notification IDs to remove:', clockInIds);
			await notifee.cancelTriggerNotifications(clockInIds);
			console.log('✅ Clock-in notifications removed:', clockInIds);
		} else {
			console.log('ℹ️ No clock-in notifications found to remove');
		}
	} catch (error) {
		console.error('❌ Error removing clock-in notifications:', error);
		// Fallback to hardcoded IDs for backward compatibility
		await notifee.cancelTriggerNotifications(['1001', '1002', '1003', '1004', '1005']);
	}
};

/**
 * Remove clock-out notifications
 * Handles dynamic IDs generated by the pattern 200{index+1}
 */
const removeClockOutNotifications = async (): Promise<void> => {
	try {
		console.log('🗑️ Removing clock-out notifications');

		// Get all trigger notifications to find clock-out ones
		const triggerNotifications = await notifee.getTriggerNotifications();
		const clockOutIds = triggerNotifications
			.filter(notification => notification.notification.id?.startsWith('200'))
			.map(notification => notification.notification.id!);

		if (clockOutIds.length > 0) {
			console.log('📋 Found clock-out notification IDs to remove:', clockOutIds);
			await notifee.cancelTriggerNotifications(clockOutIds);
			console.log('✅ Clock-out notifications removed:', clockOutIds);
		} else {
			console.log('ℹ️ No clock-out notifications found to remove');
		}
	} catch (error) {
		console.error('❌ Error removing clock-out notifications:', error);
		// Fallback to hardcoded IDs for backward compatibility
		await notifee.cancelTriggerNotifications(['2001', '2002', '2003', '2004', '2005']);
	}
};

/**
 * Start a foreground service (Android only)
 *
 * This function handles the ForegroundServiceDidNotStartInTimeException by ensuring
 * that the foreground service starts immediately after being requested.
 */
const startForegroundService = async (): Promise<void> => {
	console.log('startForegroundService called');
	if (Platform.OS === 'android') {
		try {
			// Ensure the channel exists
			await createNotificationChannels();

			// Create a notification ID for the foreground service
			const notificationId = 'foreground-service-notification';

			// First, create the foreground service notification
			const channelId = 'foreground_service';

			// Start the foreground service immediately
			// The correct way to start a foreground service is to use displayNotification with asForegroundService: true
			await notifee.displayNotification({
				id: notificationId,
				title: i18n.t('shift_ongoing'),
				body: i18n.t('have_active_shift'),
				android: {
					channelId,
					asForegroundService: true,
					ongoing: true, // Keep the notification until explicitly removed
					smallIcon: 'ic_small',
					pressAction: {
						id: 'default'
					},
					// Add importance to ensure it's visible
					importance: AndroidImportance.HIGH
				}
			});

			console.log('Foreground service started successfully');
		} catch (error) {
			console.error('Error starting foreground service:', error);
			// Try an alternative approach if the first one fails
			try {
				// Fallback to the simpler method
				await notifee.displayNotification({
					title: i18n.t('shift_ongoing'),
					body: i18n.t('have_active_shift'),
					android: {
						channelId: 'foreground_service',
						asForegroundService: true,
						ongoing: true,
						smallIcon: 'ic_small'
					}
				});
				console.log('Foreground service started with fallback method');
			} catch (fallbackError) {
				console.error('Fallback foreground service also failed:', fallbackError);
			}
		}
	}
};

/**
 * Stop the foreground service (Android only)
 *
 * This function properly cleans up the foreground service to prevent
 * ForegroundServiceDidNotStartInTimeException errors.
 */
const stopForegroundService = async (): Promise<void> => {
	if (Platform.OS === 'android') {
		try {
			// First check if the foreground service is running
			console.log('Stopping foreground service...');

			// Cancel the foreground notification first
			try {
				await notifee.cancelNotification('foreground-service-notification');
			} catch {
				console.log('No notification to cancel, continuing...');
			}

			// Then stop the foreground service
			await notifee.stopForegroundService();

			console.log('Foreground service stopped successfully');
		} catch (error) {
			console.error('Error stopping foreground service:', error);

			// Try an alternative approach if the first one fails
			try {
				// Just cancel all notifications as a fallback
				await notifee.cancelAllNotifications();
				console.log('Foreground service stopped with fallback method');
			} catch (fallbackError) {
				console.error('Fallback stop foreground service also failed:', fallbackError);
			}
		}
	}
};

// Export the public API
export default {
	initialize,
	requestPermissions,
	getFCMToken,
	displayNotification,
	initializeListeners,
	clearAllNotifications,
	clearNotificationsOnLogout,
	checkInitialNotification,
	// Badge management
	syncBadgeCount,
	clearBadgeCount,
	// Scheduled notifications
	createReminderNotification,
	removeAllReminderNotifications,
	removeClockInNotifications,
	removeClockOutNotifications,
	listScheduledNotifications,
	// Foreground service
	startForegroundService,
	stopForegroundService
};
