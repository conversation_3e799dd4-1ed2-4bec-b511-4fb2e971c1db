import React from 'react';
import { Text, TouchableOpacity, View, StyleSheet } from 'react-native';
import { HORIZONTAL_DIMENS, VERTICAL_DIMENS } from '../../../constants';
import { colors, fonts } from '../../../utils/theme';

type Props = {
	leftIcon?: React.ReactNode,
	title: string,
	rightIcon?: React.ReactNode,
	rightTitle?: string,
	handleNavigation?: () => void
}

const AccountItem = ({ leftIcon, title, rightIcon, rightTitle, handleNavigation }: Props) => {
	return (
		<TouchableOpacity
			style={styles.mainContainer}
			activeOpacity={0.8}
			onPress={handleNavigation}
		>
			{leftIcon}
			<Text style={styles.description}>
				{title}
			</Text>

			<View style={styles.iconContainer}>
				{rightIcon}
				<Text style={styles.rightTitle}>{rightTitle}</Text>
			</View>
		</TouchableOpacity>
	);
};

const styles = StyleSheet.create({
	mainContainer: {
		marginHorizontal: HORIZONTAL_DIMENS._8,
		flexDirection: 'row',
		borderBottomColor: colors.grey200,
		borderBottomWidth: 1,
		paddingVertical: VERTICAL_DIMENS._16,
		alignItems: 'center'
	},
	description: {
		marginLeft: HORIZONTAL_DIMENS._8,
		fontFamily: fonts.Montserrat.Regular,
		fontWeight: '400',
		color: colors.primary,
		fontSize: HORIZONTAL_DIMENS._16
	},
	iconContainer: {
		flex: 1,
		flexDirection: 'row-reverse',
		alignItems: 'flex-end',
		marginRight: VERTICAL_DIMENS._8
	},
	rightTitle: {
		fontFamily: fonts.Montserrat.Regular,
		fontWeight: '400',
		color: colors.grey600,
		fontSize: HORIZONTAL_DIMENS._16,
		marginRight: HORIZONTAL_DIMENS._8
	}
});

export { AccountItem };