/**
 * NotificationTestComponent.tsx
 * 
 * Example component showing how to test and monitor the duplicate notification fix.
 * This component can be used during development to verify the fix is working correctly.
 */

import React, { useEffect, useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ScrollView } from 'react-native';
import useNotifications from '../hooks/useNotifications';
import { 
	testDuplicateDetection, 
	monitorForegroundNotifications,
	analyzeNotificationTiming 
} from '../utils/notificationDebug';

const NotificationTestComponent: React.FC = () => {
	const [isMonitoring, setIsMonitoring] = useState(false);
	const [monitoringCleanup, setMonitoringCleanup] = useState<(() => void) | null>(null);
	const [testResults, setTestResults] = useState<string[]>([]);
	
	const { 
		shouldShowManualNotification, 
		generateNotificationKey,
		listScheduledNotifications 
	} = useNotifications();

	// Cleanup monitoring on unmount
	useEffect(() => {
		return () => {
			if (monitoringCleanup) {
				monitoringCleanup();
			}
		};
	}, [monitoringCleanup]);

	const addTestResult = (result: string) => {
		setTestResults(prev => [...prev.slice(-9), result]); // Keep last 10 results
	};

	const handleTestDuplicateDetection = async () => {
		addTestResult('🧪 Testing duplicate detection...');
		try {
			await testDuplicateDetection();
			addTestResult('✅ Duplicate detection test completed');
		} catch (error) {
			addTestResult(`❌ Test failed: ${error}`);
		}
	};

	const handleTestNotificationKey = () => {
		const mockMessage = {
			messageId: 'test-123',
			notification: {
				title: 'Test Notification',
				body: 'This is a test message'
			},
			data: {
				_id: 'test-data-123',
				type: 'test'
			}
		};

		const key = generateNotificationKey(mockMessage);
		addTestResult(`🔑 Generated key: ${key}`);
	};

	const handleTestShouldShow = async () => {
		const mockMessage = {
			messageId: 'should-show-test',
			notification: {
				title: 'Should Show Test',
				body: 'Testing if notification should be shown'
			},
			data: {
				type: 'test'
			}
		};

		try {
			const shouldShow = await shouldShowManualNotification(mockMessage);
			addTestResult(`📱 Should show notification: ${shouldShow}`);
		} catch (error) {
			addTestResult(`❌ Should show test failed: ${error}`);
		}
	};

	const handleToggleMonitoring = () => {
		if (isMonitoring) {
			// Stop monitoring
			if (monitoringCleanup) {
				monitoringCleanup();
				setMonitoringCleanup(null);
			}
			setIsMonitoring(false);
			addTestResult('🛑 Stopped notification monitoring');
		} else {
			// Start monitoring
			const cleanup = monitorForegroundNotifications();
			setMonitoringCleanup(() => cleanup);
			setIsMonitoring(true);
			addTestResult('👀 Started notification monitoring');
		}
	};

	const handleAnalyzeTiming = async () => {
		addTestResult('🔍 Analyzing notification timing...');
		try {
			await analyzeNotificationTiming();
			addTestResult('✅ Timing analysis completed');
		} catch (error) {
			addTestResult(`❌ Timing analysis failed: ${error}`);
		}
	};

	const handleListScheduled = async () => {
		addTestResult('📋 Listing scheduled notifications...');
		try {
			await listScheduledNotifications();
			addTestResult('✅ Scheduled notifications listed in console');
		} catch (error) {
			addTestResult(`❌ List scheduled failed: ${error}`);
		}
	};

	const clearResults = () => {
		setTestResults([]);
	};

	return (
		<ScrollView style={styles.container}>
			<Text style={styles.title}>Notification Duplicate Detection Test</Text>
			<Text style={styles.subtitle}>
				Use these buttons to test the duplicate notification fix
			</Text>

			<View style={styles.buttonContainer}>
				<TouchableOpacity 
					style={styles.button} 
					onPress={handleTestDuplicateDetection}
				>
					<Text style={styles.buttonText}>Test Duplicate Detection</Text>
				</TouchableOpacity>

				<TouchableOpacity 
					style={styles.button} 
					onPress={handleTestNotificationKey}
				>
					<Text style={styles.buttonText}>Test Key Generation</Text>
				</TouchableOpacity>

				<TouchableOpacity 
					style={styles.button} 
					onPress={handleTestShouldShow}
				>
					<Text style={styles.buttonText}>Test Should Show Logic</Text>
				</TouchableOpacity>

				<TouchableOpacity 
					style={[styles.button, isMonitoring && styles.activeButton]} 
					onPress={handleToggleMonitoring}
				>
					<Text style={styles.buttonText}>
						{isMonitoring ? 'Stop Monitoring' : 'Start Monitoring'}
					</Text>
				</TouchableOpacity>

				<TouchableOpacity 
					style={styles.button} 
					onPress={handleAnalyzeTiming}
				>
					<Text style={styles.buttonText}>Analyze Timing</Text>
				</TouchableOpacity>

				<TouchableOpacity 
					style={styles.button} 
					onPress={handleListScheduled}
				>
					<Text style={styles.buttonText}>List Scheduled</Text>
				</TouchableOpacity>

				<TouchableOpacity 
					style={[styles.button, styles.clearButton]} 
					onPress={clearResults}
				>
					<Text style={styles.buttonText}>Clear Results</Text>
				</TouchableOpacity>
			</View>

			<View style={styles.resultsContainer}>
				<Text style={styles.resultsTitle}>Test Results:</Text>
				{testResults.length === 0 ? (
					<Text style={styles.noResults}>No test results yet</Text>
				) : (
					testResults.map((result, index) => (
						<Text key={index} style={styles.resultText}>
							{result}
						</Text>
					))
				)}
			</View>

			<View style={styles.instructionsContainer}>
				<Text style={styles.instructionsTitle}>Instructions:</Text>
				<Text style={styles.instructionText}>
					1. Open console/logs to see detailed output{'\n'}
					2. Test duplicate detection to verify the fix works{'\n'}
					3. Start monitoring before sending test notifications{'\n'}
					4. Check timing analysis for scheduled notification conflicts{'\n'}
					5. All tests log detailed information to console
				</Text>
			</View>
		</ScrollView>
	);
};

const styles = StyleSheet.create({
	container: {
		flex: 1,
		padding: 20,
		backgroundColor: '#f5f5f5',
	},
	title: {
		fontSize: 24,
		fontWeight: 'bold',
		marginBottom: 10,
		textAlign: 'center',
	},
	subtitle: {
		fontSize: 16,
		color: '#666',
		marginBottom: 20,
		textAlign: 'center',
	},
	buttonContainer: {
		marginBottom: 20,
	},
	button: {
		backgroundColor: '#007AFF',
		padding: 15,
		borderRadius: 8,
		marginBottom: 10,
		alignItems: 'center',
	},
	activeButton: {
		backgroundColor: '#FF3B30',
	},
	clearButton: {
		backgroundColor: '#FF9500',
	},
	buttonText: {
		color: 'white',
		fontSize: 16,
		fontWeight: '600',
	},
	resultsContainer: {
		backgroundColor: 'white',
		padding: 15,
		borderRadius: 8,
		marginBottom: 20,
		minHeight: 150,
	},
	resultsTitle: {
		fontSize: 18,
		fontWeight: 'bold',
		marginBottom: 10,
	},
	noResults: {
		color: '#999',
		fontStyle: 'italic',
	},
	resultText: {
		fontSize: 14,
		marginBottom: 5,
		fontFamily: 'monospace',
	},
	instructionsContainer: {
		backgroundColor: '#e8f4fd',
		padding: 15,
		borderRadius: 8,
		marginBottom: 20,
	},
	instructionsTitle: {
		fontSize: 16,
		fontWeight: 'bold',
		marginBottom: 10,
	},
	instructionText: {
		fontSize: 14,
		lineHeight: 20,
	},
});

export default NotificationTestComponent;
