/**
 * useNotifications.ts
 *
 * A custom hook for handling notifications in the app.
 * This hook provides a unified interface for working with notifications
 * using the NotificationService.
 */

import { useEffect, useCallback } from 'react';
import { useNavigation } from '@react-navigation/native';
import { useAppDispatch, useAppSelector } from '../redux/hooks';
import { getUserType } from '../redux/selectors';
import { USER_TYPES } from '../constants';
import NotificationService from '../services/NotificationService';
import { getNotificationUnReadCount } from '../redux/apis/notification';
import { setTransactionList } from '../redux/features/payment-slice';

const useNotifications = () => {
	const dispatch = useAppDispatch();
	const navigation = useNavigation<any>();

	// Get user type and other relevant state
	const userType = useAppSelector(getUserType);
	const walletDetails = useAppSelector((state) => state.payment.walletDetails);
	const rewardType = useAppSelector((state) => state.reward.membershipType);

	// Initialize notification service
	useEffect(() => {
		NotificationService.initialize();
	}, []);

	/**
   * Request notification permissions
   */
	const requestPermissions = useCallback(async () => {
		return await NotificationService.requestPermissions();
	}, []);

	/**
   * Get FCM token for the device
   */
	const getFCMToken = useCallback(async () => {
		return await NotificationService.getFCMToken();
	}, []);

	/**
   * Initialize notification channels (Android only)
   */
	const createNotificationChannel = useCallback(async () => {
		// This is now handled internally by NotificationService
		// but we keep the method for backward compatibility
		await NotificationService.initialize();
	}, []);

	/**
   * Clear all notifications and reset badge count
   */
	const clearAllNotifications = useCallback(async () => {
		await NotificationService.clearAllNotifications();
	}, []);

	/**
   * Clear all notifications and badge count on logout
   */
	const clearNotificationsOnLogout = useCallback(async () => {
		await NotificationService.clearNotificationsOnLogout();
	}, []);

	/**
   * Synchronize badge count between Redux state and system badge
   */
	const syncBadgeCount = useCallback(async () => {
		await NotificationService.syncBadgeCount();
	}, []);

	/**
   * Clear badge count completely
   */
	const clearBadgeCount = useCallback(async () => {
		await NotificationService.clearBadgeCount();
	}, []);

	/**
   * Handle navigation based on notification data
   */
	const handleNavigation = useCallback((data: any) => {
		if (!data) return;

		const isWalletPresent = !!walletDetails?._id;

		switch (userType) {
			case USER_TYPES.CUSTOMER_APP:
				switch (data.type) {
					case 'NEW_PAYMENT':
						dispatch(setTransactionList([]));
						navigation.navigate('Transactions', { isWalletPresent });
						break;
					case 'NEW_PRODUCTS':
						navigation.navigate('NewProducts');
						break;
					case 'NEW_ORDER':
					case 'ORDER_RECEIVED':
					case 'ORDER_PREPARING':
					case 'ORDER_SHIPPED':
					case 'ORDER_DELIVERED':
					case 'ORDER_APPROVED':
					case 'ORDER_FOR_APPROVAL':
						navigation.navigate('TrackOrder', { orderId: data?._id });
						break;
					case 'REWARD_PAYMENT':
					case 'REWARD_PURCHASE':
					case 'REWARD_MEMBER_SCAN':
						navigation.navigate('RewardTransactions');
						break;
					case 'REWARD_SALESPERSON_VISIT':
						navigation.navigate('Account');
						break;
					case 'REWARD_MILESTONE_ONE':
					case 'REWARD_MILESTONE_TWO':
					case 'REWARD_MILESTONE_THREE':
						navigation.navigate('RewardMilestone');
						break;
					default:
						navigation.navigate('NotificationCenter');
						break;
				}
				break;

			case USER_TYPES.SALES_APP:
				switch (data.type) {
					case 'NEW_ORDER':
					case 'ORDER_RECEIVED':
					case 'ORDER_PREPARING':
					case 'ORDER_SHIPPED':
					case 'ORDER_DELIVERED':
					case 'ORDER_APPROVED':
					case 'ORDER_FOR_APPROVAL':
						navigation.navigate('TrackOrder', { orderId: data?._id });
						break;
					case 'REWARD_PAYMENT':
					case 'REWARD_PURCHASE':
					case 'REWARD_MEMBER_SCAN':
						// Navigate to reward transactions regardless of reward type
						navigation.navigate('RewardTransactions');
						break;
					default:
						navigation.navigate('NotificationCenter');
						break;
				}
				break;

			default:
				navigation.navigate('NotificationCenter');
				break;
		}
	}, [userType, walletDetails, rewardType, navigation, dispatch]);

	/**
   * Initialize notification listeners
   */
	const listenForNotifications = useCallback(() => {
		NotificationService.initializeListeners(handleNavigation);
	}, [handleNavigation]);

	/**
   * Check for initial notification that launched the app
   */
	const checkInitialNotification = useCallback(async () => {
		await NotificationService.checkInitialNotification(handleNavigation);
	}, [handleNavigation]);

	// Initialize notifications when the hook is first used
	useEffect(() => {
		// Check for initial notification on mount
		checkInitialNotification();

		// Fetch notification count
		dispatch(getNotificationUnReadCount());

		return () => {
			// No cleanup needed as NotificationService handles listener cleanup
		};
	}, [checkInitialNotification, dispatch]);

	/**
	 * Create a scheduled reminder notification
	 */
	const createReminderNotification = useCallback(async (options: any) => {
		return await NotificationService.createReminderNotification(options);
	}, []);

	/**
	 * Remove all scheduled reminder notifications
	 */
	const removeAllReminderNotifications = useCallback(async () => {
		await NotificationService.removeAllReminderNotifications();
	}, []);

	/**
	 * Debug function to list all scheduled notifications
	 */
	const listScheduledNotifications = useCallback(async () => {
		await NotificationService.listScheduledNotifications();
	}, []);

	/**
	 * Check if notification should be shown manually (for duplicate detection)
	 */
	const shouldShowManualNotification = useCallback(async (remoteMessage: any) => {
		return await NotificationService.shouldShowManualNotification(remoteMessage);
	}, []);

	/**
	 * Generate notification key for duplicate detection
	 */
	const generateNotificationKey = useCallback((remoteMessage: any) => {
		return NotificationService.generateNotificationKey(remoteMessage);
	}, []);

	/**
	 * Remove clock-in notifications
	 */
	const removeClockInNotifications = useCallback(async () => {
		await NotificationService.removeClockInNotifications();
	}, []);

	/**
	 * Remove clock-out notifications
	 */
	const removeClockOutNotifications = useCallback(async () => {
		await NotificationService.removeClockOutNotifications();
	}, []);

	/**
	 * Start a foreground service (Android only)
	 */
	const startForegroundService = useCallback(async () => {
		await NotificationService.startForegroundService();
	}, []);

	/**
	 * Stop the foreground service (Android only)
	 */
	const stopForegroundService = useCallback(async () => {
		await NotificationService.stopForegroundService();
	}, []);

	return {
		// FCM notifications
		requestPermissions,
		getFCMToken,
		listenForNotifications,
		createNotificationChannel,
		clearAllNotifications,
		clearNotificationsOnLogout,
		// Badge management
		syncBadgeCount,
		clearBadgeCount,
		// Scheduled notifications
		createReminderNotification,
		removeAllReminderNotifications,
		removeClockInNotifications,
		removeClockOutNotifications,
		listScheduledNotifications,
		// Foreground service
		startForegroundService,
		stopForegroundService,
		// Debug and duplicate detection
		shouldShowManualNotification,
		generateNotificationKey
	};
};

export default useNotifications;
