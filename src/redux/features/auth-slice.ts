import { createSlice } from '@reduxjs/toolkit';
import Toast from 'react-native-toast-message';
import i18n from '../../locales/i18n';
import { setSentryUser } from '../../utils/sentry';
import { showDangerMessage } from '../../utils/flashMessage';
import NotificationService from '../../services/NotificationService';
import {
	accessRole,
	getUserRoles,
	login,
	logout,
	verifyUser
} from '../apis/auth';

interface ObjectAny {
	[key: string]: string;
}
interface AuthState {
	isLoggedIn: boolean;
	loading: boolean;
	token: {
		accessToken: string;
		refreshToken: string;
	};
	userDetails: ObjectAny;
	userRoles: Array<any>; // Current loggedin user roles one or more
	currentRole: any; // Current loggedin user role
	syncedOnce: boolean;
	loadAppstack: boolean; // in order to delay app stack load till loader is visible to hide thread block while redux dispatching started
	syncRedux: number;
	reduxInSync: boolean;
	pullToSync: boolean;
	loadingData: boolean;
	logoutClicked: boolean;
}

const initialState: AuthState = {
	isLoggedIn: false,
	loading: false,
	token: {
		accessToken: '',
		refreshToken: ''
	},
	userDetails: {},
	userRoles: [],
	currentRole: {},
	syncedOnce: false,
	loadAppstack: false,
	syncRedux: Date.now(),
	reduxInSync: true,
	pullToSync: false,
	loadingData: true,
	logoutClicked: false
};

const authSlice = createSlice({
	name: 'auth',
	initialState,
	reducers: {
		AppLogout() {
			// Clear all notifications and badges on logout
			// This is important for auto-logout scenarios (401 errors)
			NotificationService.clearNotificationsOnLogout().catch(error => {
				console.error('Error clearing notifications during AppLogout:', error);
			});

			// state.loading = false;
			// state.isLoggedIn = false;
			// state.token = {
			// 	accessToken: '',
			// 	refreshToken: ''
			// };
			// state.userDetails = {};
			// state.userRoles = [];
			// state.currentRole = {};
			// state.syncedOnce = false;
			setSentryUser(null);
			return initialState;
		},
		setCurrentUserRole(state, action) {
			state.isLoggedIn = true;
			const currentRole = action.payload;
			state.currentRole = {
				...currentRole,
				...currentRole.role_id,
				role_id: currentRole.role_id._id,
				_id: currentRole._id
			};
			setSentryUser(state.currentRole);
		},
		updateAccessToken(state, action: any) {
			state.token.accessToken = action.payload;
		},
		setLogoutClicked(state, action) {
			state.logoutClicked = action.payload;
		},
		setSyncedOnce(state) {
			state.syncedOnce = true;
			// state.loadingData = false;
		},
		setSyncRedux(state) {
			state.syncRedux = Date.now();
		},
		setLoadAppStack(state, action) {
			state.loadAppstack = action.payload;
		},
		setReduxInSync(state, action) {
			// console.log('🚀 ~ setSyncedOnce ~ action:', action);
			state.reduxInSync = action.payload;
		},
		setPullToSync(state, action) {
			// console.log('🚀 ~ setSyncedOnce ~ action:', action);
			state.pullToSync = action.payload;
		},
		setLoadingData(
			state,
			action: {
				payload: boolean;
				type: string;
			}
		) {
			state.loadingData = action.payload;
		}
	},
	extraReducers: (builder) => {
		builder.addCase(login.pending, (state) => {
			state.loading = true;
		});
		builder.addCase(login.fulfilled, (state) => {
			state.loading = false;
		});
		builder.addCase(login.rejected, (state, action) => {
			state.loading = false;
			Toast.show({
				text1: i18n.t(action?.payload),
				type: 'error'
			});
		});
		builder.addCase(verifyUser.pending, (state) => {
			state.loading = true;
		});
		builder.addCase(verifyUser.fulfilled, (state, action: any) => {
			const data = action.payload.data;
			const tokens = data.tokens;
			if (data.userRoles.length === 1) {
				state.isLoggedIn = true;
			}
			state.token = {
				accessToken: tokens.accessToken,
				refreshToken: tokens.refreshToken
			};
			state.userDetails = data.userDetails
				? data.userDetails
				: data.customerDetails; // Set user details when otp verified
			// console.log('userDetails', JSON.stringify(state.userDetails));
			state.userRoles = data.userRoles;
			const currentRole = data.userRoles[0];
			/* Set default current role when otp verified */
			state.currentRole = {
				...currentRole,
				...currentRole.role_id,
				role_id: currentRole.role_id._id,
				_id: currentRole._id
			};
			if (state.isLoggedIn) {
				setSentryUser(state.currentRole);
			}
			state.loading = false;
		});
		builder.addCase(verifyUser.rejected, (state) => {
			state.loading = false;
			showDangerMessage(i18n.t('verification_failed'));
		});
		builder.addCase(getUserRoles.pending, (state) => {
			state.loading = true;
		});
		builder.addCase(getUserRoles.rejected, (state) => {
			state.loading = false;
		});
		builder.addCase(getUserRoles.fulfilled, (state, action) => {
			state.userRoles = action.payload.data.userRoles;
			//console.log('state.userRoles', state.userRoles);
			state.loading = false;
		});
		builder.addCase(accessRole.pending, (state) => {
			state.loading = true;
		});
		builder.addCase(accessRole.rejected, (state, action) => {
			state.loading = false;
			Toast.show({
				text1: i18n.t(action?.payload),
				type: 'error'
			});
		});
		builder.addCase(accessRole.fulfilled, (state) => {
			state.loading = false;
		});
		builder.addCase(logout.fulfilled, (state, action) => {
			console.log('logout action', action.payload);
			setSentryUser(null);
			return initialState;
		});
		builder.addCase(logout.rejected, (state, action) => {
			console.log('logout action', action.payload);
			setSentryUser(null);
			return initialState;
		});
	}
});

export const {
	AppLogout,
	setCurrentUserRole,
	updateAccessToken,
	setSyncedOnce,
	setSyncRedux,
	setPullToSync,
	setReduxInSync,
	setLoadingData,
	setLoadAppStack,
	setLogoutClicked
} = authSlice.actions;
export default authSlice.reducer;
