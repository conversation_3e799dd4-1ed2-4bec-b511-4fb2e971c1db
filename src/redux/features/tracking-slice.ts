import AsyncStorage from '@react-native-async-storage/async-storage';
import { createSlice } from '@reduxjs/toolkit';
import { persistReducer } from 'redux-persist';
import { formatInTimeZone } from 'date-fns-tz';
import { differenceInMilliseconds } from 'date-fns';
import { isPointWithinRadius } from 'geolib';
import { addActivityLogs, getCurrentShiftActivity, salesPersonWorkShift, updateActivityLogs, visitImages } from '../apis/tracking';
import { watchLocation } from '../../utils/location';
import { ACTIVITY_TYPE, CLOCKED_TYPE, FENCE_TYPES, GEOFENCE_CLOCKIN_TYPE, headingOptions } from '../../constants';
import { addMinutesToTime, getCurrentDayInTimeZone } from '../../utils/functions';
import { ActiveActivity, ActiveShift } from '../types';
import LocationTracking from '../../utils/locationTracking';
import NotificationService from '../../services/NotificationService';

interface ObjectAny {
	[key: string]: any;
}

interface TrackingState {
	activeShift: ActiveShift | undefined; // Stores shift info for ongoing shift otherwise undefined
	activeActivity: ActiveActivity | undefined; // Stores activity info for ongoing shift otherwise undefined
	shiftStatus: ObjectAny;
	isStartCustomerVisit: boolean;
	customerVisitOption: ObjectAny;
	selectCustomerForVisit: boolean;
	selectedCustomerForVisit: ObjectAny;
	isStartShiftVisible: boolean;
	isStartShiftEnable: boolean;
	currentLocation: {
		latitude: any;
		longitude: any;
		heading: any;
	} | undefined;
	geofenceInfo: undefined | ObjectAny;
	visitObjectivesInfo: ObjectAny;
	purposeObjective: Array<any>;
	feedbackObjective: Array<any>;
	startShiftOption: Array<any>;
	isEndShiftVisible: boolean;
	isCashPayment: boolean;
	isTerminalPayment: boolean;
	startShiftAutoLocation: {
		latitude: any;
		longitude: any;
		heading: any;
	} | undefined;
}

export const trackingInitialState: TrackingState = {
	activeShift: undefined,
	activeActivity: undefined,
	shiftStatus: {},
	isStartCustomerVisit: false,
	customerVisitOption: {},
	selectCustomerForVisit: false,
	selectedCustomerForVisit: {},
	isStartShiftVisible: false,
	isStartShiftEnable: false,
	currentLocation: undefined,
	geofenceInfo: undefined,
	visitObjectivesInfo: {},
	purposeObjective: [],
	feedbackObjective: [],
	startShiftOption: [],
	isEndShiftVisible: false,
	isCashPayment: false,
	isTerminalPayment: false,
	startShiftAutoLocation: undefined
};

const trackingSlice = createSlice({
	name: 'tracking',
	initialState: trackingInitialState,
	reducers: {
		setShiftStatus(state, action) {
			state.shiftStatus = action.payload;
		},
		setIsStartCustomerVisit(state, action) {
			state.isStartCustomerVisit = action.payload;
		},
		setCustomerVisitOption(state, action) {
			state.customerVisitOption = action.payload;
		},
		setSelectCustomerForVisit(state, action) {
			state.selectCustomerForVisit = action.payload;
		},
		setSelectedCustomerForVisit(state, action) {
			state.selectedCustomerForVisit = action.payload;
		},
		clearTrackingStates(state) {
			state.shiftStatus = {};
			state.isStartCustomerVisit = false;
			state.customerVisitOption = {};
			state.selectCustomerForVisit = false;
			state.selectedCustomerForVisit = {};
		},
		setIsEndShiftVisible(state, action) {
			state.isEndShiftVisible = action.payload;
		},
		setUserCurrentLocation(state, action) {
			const currentLocation = action.payload;
			state.currentLocation = currentLocation;
			const geofenceInfo = state.geofenceInfo;
			if (geofenceInfo && state.isStartShiftVisible && currentLocation && geofenceInfo.clock_in_type === GEOFENCE_CLOCKIN_TYPE.LOCATION) {
				const isUserWithinRadius = isPointWithinRadius(
					{ latitude: currentLocation?.latitude, longitude: currentLocation?.longitude },
					{ latitude: geofenceInfo?.gps_coordinates?.latitude, longitude: geofenceInfo?.gps_coordinates?.longitude },
					geofenceInfo?.fence_size
				);

				if (isUserWithinRadius && geofenceInfo.fence_type === FENCE_TYPES.UPON_ARRIVAL) {
					state.isStartShiftEnable = true;
				} else if (!isUserWithinRadius && geofenceInfo.fence_type === FENCE_TYPES.UPON_LEAVING) {
					state.isStartShiftEnable = true;
				} else {
					state.isStartShiftEnable = false;
				}
			} else if (geofenceInfo && state.isStartShiftVisible && geofenceInfo.clock_in_type === GEOFENCE_CLOCKIN_TYPE.AUTO) {
				state.isStartShiftVisible = true;
				state.isStartShiftEnable = true;
			}
			if (state.activeShift) {
				LocationTracking.updateCurrentLocation(currentLocation, state.activeShift, state.activeActivity);
			}
		},
		setIsCashPayment(state, action) {
			state.isCashPayment = action.payload;
		},
		setIsTerminalPayment(state, action) {
			state.isTerminalPayment = action.payload;
		},
		setStartShiftAutoLocation(state, action) {
			state.startShiftAutoLocation = action.payload;
		}
	},
	extraReducers: (builder) => {
		builder.addCase(salesPersonWorkShift.fulfilled, (state, action) => {
			const data = action.payload.data;

			if (data && Object.keys(data).length > 0) {
				const tenantTimeZone = action.payload.tenantTimeZone;
				// console.log('tenantTimeZone', tenantTimeZone);
				const arr1 = data.destinationInfo;
				const arr2 = headingOptions;
				state.startShiftOption = arr1 ? arr1.concat(arr2) : arr2;

				const currentTime = formatInTimeZone(new Date(), tenantTimeZone, 'HH:mm:ss'); // Get current time for tenant timezone
				const currentDay = getCurrentDayInTimeZone(new Date(), tenantTimeZone);
				// const currentTime = format(new Date(), 'HH:mm:ss');
				const shifts = data.workTimeInfo?.shifts;
				const reminder = data.workTimeInfo.reminder;
				const weekend = data.workTimeInfo?.weekend;
				shifts.forEach((e: { shift_start: string; shift_end: string; auto_clock_out: any }) => {
					const clockOut = e.auto_clock_out * 60;
					const autoClockOutTime = addMinutesToTime(e.shift_end, clockOut);
					if (currentTime >= e.shift_start && currentTime <= autoClockOutTime && !weekend?.includes(currentDay?.toUpperCase())) {
						// Show start shift button
						state.isStartShiftVisible = true;
					} else if (currentTime < e.shift_start) {
						// console.log('e.shift_start', e.shift_start, typeof e.shift_start);
						const [hours1, minutes1, seconds1] = currentTime.split(':');
						const [hours2, minutes2] = e.shift_start.split(':');
						const current = new Date(2022, 0, 0, +hours1, +minutes1, +seconds1);
						const startTime = new Date(2022, 0, 0, +hours2, +minutes2, 0);
						const diffInMs = startTime.getTime() - current.getTime();
						// console.log('diffIn Seconds', diffInMs / 1000);
						if (diffInMs) {
							LocationTracking.setShiftStartTimeout(diffInMs); // Set timeout for get start shift button
						}
					} else {
						state.isStartShiftVisible = false;
						state.isStartShiftEnable = false;
					}
				});

				// If has reminder enabled
				if (reminder) {
					console.log('📋 Setting up shift reminders:', {
						clockInBefore: reminder.clock_in_before,
						clockOutAfter: reminder.clock_out_after,
						shiftsCount: shifts.length
					});

					if (reminder.clock_in_before) {
						console.log('⏰ Creating clock-in reminders...');
						shifts.forEach((shift: any, index: number) => {
							const notificationId = `100${index + 1}`;
							console.log(`📅 Scheduling clock-in reminder ${index + 1}:`, {
								notificationId,
								shiftName: shift.shift_name,
								shiftStart: shift.shift_start,
								reminderMinutes: reminder.clock_in_before
							});

							NotificationService.createReminderNotification({
								shiftName: shift.shift_name,
								shiftTime: shift.shift_start,
								clockMinutes: reminder.clock_in_before,
								clockType: 'clock_in',
								notificationId
							});
						});
					} else {
						NotificationService.removeClockInNotifications(); // Remove earlier created clock in notificaitons if available
					}

					if (reminder.clock_out_after) {
						console.log('⏰ Creating clock-out reminders...');
						shifts.forEach((shift: any, index: number) => {
							const notificationId = `200${index + 1}`;
							console.log(`📅 Scheduling clock-out reminder ${index + 1}:`, {
								notificationId,
								shiftName: shift.shift_name,
								shiftEnd: shift.shift_end,
								reminderMinutes: reminder.clock_out_after
							});

							NotificationService.createReminderNotification({
								shiftName: shift.shift_name,
								shiftTime: shift.shift_end,
								clockMinutes: reminder.clock_out_after,
								clockType: 'clock_out',
								notificationId
							});
						});
					} else {
						NotificationService.removeClockOutNotifications(); // Remove earlier created clock in notificaitons if available
					}
				} else {
					console.log('🗑️ No reminders configured, removing all existing reminder notifications');
					NotificationService.removeAllReminderNotifications(); // Remove previous reminder notifications if created
				}

				// List all scheduled notifications for debugging
				setTimeout(() => {
					NotificationService.listScheduledNotifications();
				}, 1000); // Small delay to ensure all notifications are created

				// onCreateTriggerNotification();

				const geofenceInfo = data.geoFenceInfo;
				// console.log('geofenceInfo:', geofenceInfo);

				if (geofenceInfo) {
					state.geofenceInfo = geofenceInfo;
					if (!state.activeShift) {
						watchLocation({}); // If has geofence then start watch on location change to fullfill geofence criteria
					}
				}

				// If no geofence and start shift visible then enable start shift button
				if (!geofenceInfo && state.isStartShiftVisible) state.isStartShiftEnable = true;

				const currentLocation = state.currentLocation;
				if (geofenceInfo && state.isStartShiftVisible && currentLocation && geofenceInfo.clock_in_type === GEOFENCE_CLOCKIN_TYPE.LOCATION) {
					const isUserWithinRadius = isPointWithinRadius(
						{ latitude: currentLocation?.latitude, longitude: currentLocation?.longitude },
						{ latitude: geofenceInfo.gps_coordinates?.latitude, longitude: geofenceInfo.gps_coordinates?.longitude },
						geofenceInfo.fence_size
					);
					// console.log('isUserWithinRadius:', isUserWithinRadius);
					if (isUserWithinRadius && geofenceInfo.fence_type === FENCE_TYPES.UPON_ARRIVAL) {
						// If current location inside radius and fence type is upon arrival then enable start shift button
						state.isStartShiftEnable = true;
					} else if (!isUserWithinRadius && geofenceInfo.fence_type === FENCE_TYPES.UPON_LEAVING) {
						// If current location is outside radius and fence type is upon leaving then enable start shift button
						state.isStartShiftEnable = true;
					} else {
						state.isStartShiftEnable = false;
					}
				} else if (geofenceInfo && state.isStartShiftVisible && geofenceInfo.clock_in_type === GEOFENCE_CLOCKIN_TYPE.AUTO) {
					state.isStartShiftVisible = true;
					state.isStartShiftEnable = true;
				}

				const visitInfo = data.visitInfo;
				if (visitInfo) {
					state.visitObjectivesInfo = visitInfo;
					state.purposeObjective = visitInfo.objectives.filter((e: any) => e.type.purpose);
					state.feedbackObjective = visitInfo.objectives.filter((e: any) => e.type.feedback);
				}
			}
		});
		builder.addCase(salesPersonWorkShift.rejected, (state, action) => {
			if (action?.payload?.message === 'access_denied') {
				state.activeShift = undefined;
				state.activeActivity = undefined;
				state.isStartShiftVisible = false;
			}
		});
		builder.addCase(addActivityLogs.fulfilled, (state, action) => {
			const data = action.payload.data;
			// console.log('data', JSON.stringify(data));
			// state.activeActivity = !data?.clock_out && data;
			const specificActivities = [ACTIVITY_TYPE.HEADING_TO_CUSTOMER, ACTIVITY_TYPE.HEADING_TO_OFFICE, ACTIVITY_TYPE.HEADING_TO_NEW_CUSTOMER, ACTIVITY_TYPE.VISIT];
			if (specificActivities.includes(data.activity_type) && !data?.clock_out) {
				state.activeActivity = data;
				const currentLocation: any = state.currentLocation;
				LocationTracking.updateActivityId(currentLocation, data); // Update activity id on firestore
			}
			if (data.activity_type === ACTIVITY_TYPE.CLOCKED) {
				// console.log('shift started', data);
				state.activeShift = data;
				const currentDate = new Date();
				const autoClockOutDate = new Date(data.auto_clock_out);
				const autoClockOutDifference = differenceInMilliseconds(autoClockOutDate, currentDate);
				// console.log('autoClockOutDifference in seconds', autoClockOutDifference / 1000);
				if (autoClockOutDifference && autoClockOutDifference > 0) {
					LocationTracking.setShiftStartTimeout(autoClockOutDifference); // Set timeout for get start shift button
				}
			}
		});
		builder.addCase(updateActivityLogs.fulfilled, (state, action) => {
			const requestPayload = action.meta.arg;
			const data = action.payload.data;

			state.activeActivity = !data?.clock_out && data;
			if (requestPayload.activityType === ACTIVITY_TYPE.CLOCKED && requestPayload.actionType === CLOCKED_TYPE.CLOCKED_OUT) {
				state.activeActivity = undefined;
				state.activeShift = undefined;
				const currentLocation: any = state.currentLocation;
				LocationTracking.updateActivityId(currentLocation, undefined); // Remove activity id on firestore
			} else {
				state.activeActivity = undefined;
				const currentLocation: any = state.currentLocation;
				LocationTracking.updateActivityId(currentLocation, undefined); // Remove activity id on firestore
			}
		});
		builder.addCase(getCurrentShiftActivity.fulfilled, (state, action) => {
			const data = action.payload.data;
			// console.log('data:', JSON.stringify(data));

			if (data && Object.keys(data).length > 0) {
				watchLocation({ canStartForegroundService: true }); // Start location tracking on if shift is already started
				const activeShift = { ...data };
				delete activeShift.activity;
				// console.log('activeShift', activeShift);
				state.activeShift = activeShift;
				state.activeActivity = !data.activity?.clock_out && data.activity; // Set active activity if activity not clocked out yet

				if (data.activity?.activity_type === ACTIVITY_TYPE.VISIT && !data.activity?.clock_out) {
					state.isStartCustomerVisit = true;
				}

				const currentDate = new Date();
				const autoClockOutDate = new Date(activeShift.auto_clock_out);
				const autoClockOutDifference = differenceInMilliseconds(autoClockOutDate, currentDate);
				// console.log('autoClockOutDifference in seconds', autoClockOutDifference / 1000);
				if (autoClockOutDifference && autoClockOutDifference > 0) {
					LocationTracking.setShiftStartTimeout(autoClockOutDifference); // Set timeout for get start shift button
				}
			} else {
				state.activeActivity = undefined;
				state.activeShift = undefined;
				NotificationService.stopForegroundService(); // If foreground service is running then stop
			}
		});
		builder.addCase(getCurrentShiftActivity.rejected, (state, action) => {
			if (action?.payload?.message === 'access_denied') {
				state.activeShift = undefined;
				state.activeActivity = undefined;
				state.isStartShiftVisible = false;
			}
		});
		builder.addCase(visitImages.fulfilled, (state, action) => {
			const data = action.payload.data;
			// console.log('data:', JSON.stringify(data));

			state.activeActivity = !data.clock_out && data;
		});
	}
});

const persistConfig = {
	key: 'tracking',
	storage: AsyncStorage,
	whitelist: ['shiftStatus', 'isStartCustomerVisit', 'customerVisitOption', 'selectedCustomerForVisit', 'visitObjectivesInfo', 'purposeObjective', 'feedbackObjective', 'isEndShiftVisible', 'startShiftAutoLocation'] // Persist some values
};

export const {
	setShiftStatus,
	setIsStartCustomerVisit,
	setCustomerVisitOption,
	setSelectCustomerForVisit,
	setSelectedCustomerForVisit,
	clearTrackingStates,
	setUserCurrentLocation,
	setIsEndShiftVisible,
	setIsCashPayment,
	setIsTerminalPayment,
	setStartShiftAutoLocation
} = trackingSlice.actions;
export default persistReducer(persistConfig, trackingSlice.reducer);