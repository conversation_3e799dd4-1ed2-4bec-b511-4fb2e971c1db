import { createSelector } from '@reduxjs/toolkit';
import { USER_TYPES } from '../../constants';
import { RootState } from '../store';

/* -------------------------------------------------------------------------- */
/*                       It used in both offline and online                   */
/* -------------------------------------------------------------------------- */

// Normal selector to get the auth slice
const selectAuth = (state: RootState) => state.auth;

// Memoized selector to get the sales person ID
export const getSalesPersonId = createSelector([selectAuth], auth => {
	const currentRole = auth.currentRole;
	if (currentRole.portal_type === USER_TYPES.CUSTOMER_APP) {
		return currentRole.sales_person_id;
	} else if (currentRole.portal_type === USER_TYPES.SALES_APP) {
		return currentRole._id;
	} else {
		return '';
	}
});

/* -------------------------------------------------------------------------- */
/*                       It used in both offline and online                   */
/* -------------------------------------------------------------------------- */

// Memoized selector to get the user type
export const getUserType = createSelector([selectAuth], auth => {
	const currentRole = auth.currentRole;
	if (currentRole.portal_type === USER_TYPES.TENANT_PORTAL) {
		if (currentRole.name === 'Tenant Owner') {
			return USER_TYPES.TENANT_OWNER;
		}
		if (currentRole.name === 'Admin') {
			return USER_TYPES.ADMIN;
		}
	} else {
		return currentRole.portal_type;
	}
});

/* -------------------------------------------------------------------------- */
/*                       It used in both offline and online                   */
/* -------------------------------------------------------------------------- */

// Memoized selector to get the tenant country
export const getTenantCountry = createSelector([selectAuth], auth => {
	const currentRole = auth.currentRole;
	return currentRole.tenant_id?.country;
});

/* -------------------------------------------------------------------------- */
/*                       It used in both offline and online                   */
/* -------------------------------------------------------------------------- */

// Memoized selector to get the tenant currency
export const getTenantCurrency = createSelector([selectAuth], auth => {
	const currentRole = auth.currentRole;
	return currentRole.tenant_id?.country.currency;
});

/* -------------------------------------------------------------------------- */
/*                       It used in both offline and online                   */
/* -------------------------------------------------------------------------- */

// Memoized selector to get the languages
export const getLanguages = createSelector([selectAuth], auth => {
	const country = auth.currentRole.tenant_id?.country;
	const languages = [{ name: 'English', value: 'en' }];
	if (country === undefined) {
		return languages;
	}
	if (country.secondary_language_code !== 'en') {
		languages.push({
			name: country.secondary_language_name,
			value: country.secondary_language_code
		});
	}
	return languages;
});

/* -------------------------------------------------------------------------- */
/*                       It used in both offline and online                   */
/* -------------------------------------------------------------------------- */

// Normal selector to get the customer slice
const selectCustomer = (state: RootState) => state.customer;

// Normal selector to get the setting slice
const selectSetting = (state: RootState) => state.setting;

// Memoized selector to get the price list ID
export const getPriceListId = createSelector(
	[selectAuth, selectCustomer, selectSetting],
	(auth, customer, setting) => {
		const userType = auth.currentRole.portal_type;
		const masterPriceId = setting.userSettings.default_master_price_id;

		if (
			userType === USER_TYPES.SALES_APP ||
			userType === USER_TYPES.TENANT_PORTAL ||
			userType === USER_TYPES.SUPERVISOR_APP
		) {
			const activeCustomer = customer.activeCustomer;
			if (activeCustomer !== null) {
				return activeCustomer.price_list_id;
			} else if (masterPriceId) {
				return masterPriceId;
			} else {
				const priceList = customer.priceList.find(x => x.is_default === true);
				if (priceList) {
					return priceList.value;
				} else {
					return customer.priceList[0]?.value;
				}
			}
		} else if (userType === USER_TYPES.CUSTOMER_APP) {
			return auth.currentRole.price_list_id;
		}
		return '';
	}
);

/* -------------------------------------------------------------------------- */
/*     It used in both offline and online. Need to verify the conditions      */
/* -------------------------------------------------------------------------- */

// Memoized selector to get the branch ID
export const getBranchId = createSelector(
	[selectAuth, selectCustomer],
	(auth, customer) => {
		const userType = auth.currentRole.portal_type;
		if (
			userType === USER_TYPES.SALES_APP ||
			userType === USER_TYPES.CUSTOMER_APP
		) {
			return auth.currentRole.branch_id._id;
		} else if (customer.activeCustomer === null) {
			return auth.currentRole.defaultBranch;
		} else {
			return customer.activeCustomer?.sales_person_id?.branch_id;
		}
	}
);

/* -------------------------------------------------------------------------- */
/*                       It used in both offline and online                   */
/* -------------------------------------------------------------------------- */

// Memoized selector to get the customer user role ID
export const getCustomerUserRoleId = createSelector(
	[selectAuth, selectCustomer],
	(auth, customer) => {
		const userType = auth.currentRole.portal_type;
		const activeCustomer = customer.activeCustomer;
		return userType === USER_TYPES.CUSTOMER_APP
			? auth.currentRole._id
			: activeCustomer?._id;
	}
);

/* -------------------------------------------------------------------------- */
/*                       It used in both offline and online                   */
/* -------------------------------------------------------------------------- */

// Memoized selector to get the sales person role ID
export const getSalesPersonRoleId = createSelector(
	[selectAuth, selectCustomer],
	(auth, customer) => {
		const userType = auth.currentRole.portal_type;
		const activeCustomer = customer.activeCustomer;
		if (userType === USER_TYPES.SALES_APP) {
			return auth.currentRole._id;
		} else if (userType === USER_TYPES.CUSTOMER_APP) {
			return auth.currentRole.sales_person_id;
		} else {
			return activeCustomer?.sales_person_id;
		}
	}
);

/* -------------------------------------------------------------------------- */
/*                       It used in both offline and online                   */
/* -------------------------------------------------------------------------- */

// Memoized selector to get the username
export const getUsername = createSelector([selectAuth], auth => {
	const userType = auth.currentRole.portal_type;
	const userDetails = auth.userDetails;
	if (userType === USER_TYPES.SALES_APP) {
		return `${userDetails.first_name} ${userDetails.last_name}`;
	} else if (userType === USER_TYPES.CUSTOMER_APP) {
		return `${auth.currentRole.customer_first_name} ${auth.currentRole.customer_last_name}`;
	} else {
		return `${userDetails.first_name} ${userDetails.last_name}`;
	}
});

/* -------------------------------------------------------------------------- */
/*                       It used in both offline and online                   */
/* -------------------------------------------------------------------------- */

// Memoized selector to get the customer external ID
export const getCustomerExternalId = createSelector(
	[selectAuth, selectCustomer],
	(auth, customer) => {
		const userType = auth.currentRole.portal_type;
		if (userType === USER_TYPES.CUSTOMER_APP) {
			return auth.currentRole?.external_id;
		} else {
			return customer.activeCustomer?.external_id;
		}
	}
);

/* -------------------------------------------------------------------------- */
/*                       It used in both offline and online                   */
/* -------------------------------------------------------------------------- */

// Memoized selector to get the customer name
export const getCustomerName = createSelector(
	[selectAuth, selectCustomer],
	(auth, customer) => {
		const userType = auth.currentRole.portal_type;
		if (userType === USER_TYPES.CUSTOMER_APP) {
			return auth.currentRole.customer_name;
		} else {
			return customer.activeCustomer?.customer_name;
		}
	}
);

/* -------------------------------------------------------------------------- */
/*                       It used in both offline and online                   */
/* -------------------------------------------------------------------------- */

// Memoized selector to get the customer legal name
export const getCustomerLegalName = createSelector(
	[selectAuth, selectCustomer],
	(auth, customer) => {
		const userType = auth.currentRole.portal_type;
		if (userType === USER_TYPES.CUSTOMER_APP) {
			return auth.currentRole.customer_legal_name;
		} else {
			return customer.activeCustomer?.customer_legal_name;
		}
	}
);

/* -------------------------------------------------------------------------- */
/*                       It used in both offline and online                   */
/* -------------------------------------------------------------------------- */

// Memoized selector to get the primary contact name
export const getPrimaryContactName = createSelector(
	[selectAuth, selectCustomer],
	(auth, customer) => {
		const userType = auth.currentRole.portal_type;
		if (userType === USER_TYPES.CUSTOMER_APP) {
			return `${auth.currentRole.customer_first_name} ${auth.currentRole.customer_last_name}`;
		} else {
			return `${customer.activeCustomer?.customer_first_name} ${customer.activeCustomer?.customer_last_name}`;
		}
	}
);

/* -------------------------------------------------------------------------- */
/*                       It used in both offline and online                   */
/* -------------------------------------------------------------------------- */

// Memoized selector to get the sales person name
export const getSalesPersonName = createSelector(
	[selectAuth, selectCustomer],
	(auth, customer) => {
		const userType = auth.currentRole.portal_type;
		if (userType === USER_TYPES.SALES_APP) {
			return `${auth.userDetails.first_name} ${auth.userDetails.last_name}`;
		} else if (userType === USER_TYPES.CUSTOMER_APP) {
			return `${auth.currentRole.salesperson.first_name} ${auth.currentRole.salesperson.last_name}`;
		} else {
			return `${customer.activeCustomer?.salesPerson.first_name} ${customer.activeCustomer?.salesPerson.last_name}`;
		}
	}
);
