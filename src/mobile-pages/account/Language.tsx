import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Text, TouchableOpacity, View, StyleSheet } from 'react-native';
import { CheckBox, PrimaryButton } from '../../components/common';
import { HORIZONTAL_DIMENS, VERTICAL_DIMENS } from '../../constants';
import { colors, fonts } from '../../utils/theme';
import { useAppSelector } from '../../redux/hooks';
import { getLanguages } from '../../redux/selectors';
import { changeAppLanguage } from '../../utils/languageUtils';

const Language = () => {
	const { t, i18n } = useTranslation();
	const [language, setLanguage] = useState(i18n.language);
	const languages = useAppSelector(getLanguages);

	const handleLanguage = async () => {
		// Then change the app language and handle RTL
		await changeAppLanguage(language);

	};

	return (
		<View style={styles.mainContainer}>
			<View style={{ marginTop: VERTICAL_DIMENS._16, backgroundColor: colors.white }}>
				{
					languages.map((item, index) => {
						const isLast = languages.length - 1 === index;
						return (
							<TouchableOpacity
								style={[styles.languageContainer, !isLast && styles.itemBottomBorder]}
								onPress={() => setLanguage(item.value)}
								key={item.value}
							>
								<Text style={styles.languageContentTitle}>{item.name}</Text>
								<CheckBox
									checked={language === item.value}
									onChange={() => setLanguage(item.value)}
								/>
							</TouchableOpacity>
						);
					})
				}
			</View>
			<View style={styles.buttonContainer}>
				<PrimaryButton
					title={t('confirm')}
					titleStyle={styles.titleStyle}
					onPress={handleLanguage}
					style={styles.confirmButton}
				/>
			</View>
		</View>

	);
};
export default Language;

const styles = StyleSheet.create({
	mainContainer: {
		flex: 1
	},
	languageContainer: {
		paddingVertical: VERTICAL_DIMENS._14,
		flexDirection: 'row',
		marginHorizontal: HORIZONTAL_DIMENS._16,
		justifyContent: 'space-between'
	},
	itemBottomBorder: {
		borderBottomColor: colors.grey200,
		borderBottomWidth: 1
	},
	languageContentTitle: {
		fontFamily: fonts.Montserrat.Regular,
		fontSize: HORIZONTAL_DIMENS._16,
		fontWeight: '400',
		color: colors.primary
	},
	confirmButton: {
		marginHorizontal: HORIZONTAL_DIMENS._40
	},
	buttonContainer: {
		position: 'absolute',
		width: '100%',
		bottom: VERTICAL_DIMENS._34
	},
	titleStyle: {
		textTransform: 'uppercase'
	}
});