import { I18n<PERSON>anager, StyleSheet } from 'react-native';
import { HORIZONTAL_DIMENS, VERTICAL_DIMENS, _DEVICE_WIDTH } from '../../constants';
import { colors, fonts } from '../../utils/theme';

export default StyleSheet.create({
	footerContainer: {
		position: 'absolute',
		bottom: 0,
		width: _DEVICE_WIDTH,
		backgroundColor: colors.backdropBlur,
		paddingHorizontal: HORIZONTAL_DIMENS._40,
		paddingTop: VERTICAL_DIMENS._12,
		paddingBottom: VERTICAL_DIMENS._16
	},
	headerBackStyle: {

	},
	mainContainer: {
		flex: 1,
		backgroundColor: colors.white
	},
	loadingContainer: {
		flex: 1,
		alignItems: 'center',
		justifyContent: 'center'
	},
	header: {
		flexDirection: 'row',
		alignItems: 'center',
		justifyContent: 'space-between',
		zIndex: 1111
	},
	headerTitle: {
		fontFamily: fonts.Montserrat.SemiBold,
		fontWeight: '600',
		fontSize: HORIZONTAL_DIMENS._20,
		color: colors.black
	},
	selectCustomerBtnText: {
		textTransform: 'uppercase'
	},
	emptyCartContainer: {
		flex: 1,
		alignSelf: 'center',
		justifyContent: 'center',
		alignItems: 'center'
	},
	emptyCartHeader: {
		fontFamily: fonts.Montserrat.SemiBold,
		fontSize: HORIZONTAL_DIMENS._24,
		fontWeight: '600',
		lineHeight: HORIZONTAL_DIMENS._27,
		letterSpacing: -1,
		textAlign: 'center',
		color: colors.primary,
		marginVertical: VERTICAL_DIMENS._16,
		marginTop: VERTICAL_DIMENS._32,
		marginHorizontal: HORIZONTAL_DIMENS._16
	},
	emptyCartMessage: {
		fontFamily: fonts.Montserrat.Regular,
		fontSize: HORIZONTAL_DIMENS._16,
		fontWeight: '400',
		lineHeight: 30,
		textAlign: 'center',
		color: colors.grey500,
		marginHorizontal: HORIZONTAL_DIMENS._40,
		marginVertical: VERTICAL_DIMENS._8
	},
	orderDetailsContainer: {
		borderBottomColor: colors.grey200,
		borderBottomWidth: 1,
		flexDirection: 'row',
		justifyContent: 'space-between'
	},
	orderDetails: {
		fontFamily: fonts.Montserrat.Medium,
		fontSize: HORIZONTAL_DIMENS._16,
		fontWeight: '500',
		color: colors.primary,
		marginHorizontal: HORIZONTAL_DIMENS._16,
		paddingVertical: VERTICAL_DIMENS._14,
		textAlign: I18nManager.isRTL ? 'left' : 'auto',
		writingDirection: I18nManager.isRTL ? 'rtl' : 'ltr'
	},
	flatlistContainer: {
		backgroundColor: colors.white,
		paddingBottom: VERTICAL_DIMENS._80
	},
	totalContainer: {
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
		padding: 15,
		backgroundColor: colors.grey100
	},
	totalText: {
		fontFamily: fonts.Montserrat.Medium,
		fontSize: HORIZONTAL_DIMENS._16,
		color: colors.primary,
		fontWeight: '500'
	},
	postFixText: {
		fontFamily: fonts.Montserrat.Medium,
		color: colors.primary,
		fontSize: HORIZONTAL_DIMENS._11,
		fontWeight: '500'
	},
	swipeContainer: {
		flex: 1,
		backgroundColor: colors.error,
		justifyContent: 'center',
		alignItems: 'center',
		width: HORIZONTAL_DIMENS._80
	},
	productImage: {
		height: 68,
		width: 68
	},
	imageView: {
		marginRight: HORIZONTAL_DIMENS._16
	},
	swipableItemContainer: {
		backgroundColor: colors.white,
		flexDirection: 'row',
		paddingVertical: VERTICAL_DIMENS._10,
		marginHorizontal: HORIZONTAL_DIMENS._16,
		borderBottomColor: colors.grey200,
		borderBottomWidth: 1
	},
	swipableItem: {
		flexDirection: 'row',
		paddingVertical: VERTICAL_DIMENS._10,
		marginHorizontal: HORIZONTAL_DIMENS._16,
		borderBottomColor: colors.grey200,
		borderBottomWidth: 1,
		backgroundColor: colors.white
	},
	rightTopContainer: {
		width: HORIZONTAL_DIMENS._250
	},
	productName: {
		fontFamily: fonts.Montserrat.Regular,
		fontSize: HORIZONTAL_DIMENS._14,
		fontWeight: '400',
		color: colors.primary,
		width: HORIZONTAL_DIMENS._200,
		marginBottom: VERTICAL_DIMENS._8,
		textAlign: I18nManager.isRTL ? 'left' : 'auto',
		writingDirection: I18nManager.isRTL ? 'rtl' : 'ltr'
	},
	giftProductName: {
		fontFamily: fonts.Montserrat.Regular,
		fontSize: HORIZONTAL_DIMENS._12,
		fontWeight: '400',
		color: colors.primary,
		width: HORIZONTAL_DIMENS._200,
		marginBottom: VERTICAL_DIMENS._8,
		textAlign: I18nManager.isRTL ? 'left' : 'auto',
		writingDirection: I18nManager.isRTL ? 'rtl' : 'ltr'
	},
	rightBottomContainer: {
		width: HORIZONTAL_DIMENS._250,
		flexDirection: 'row',
		alignItems: 'flex-end',
		justifyContent: 'space-between'
	},
	originalPriceText: {
		color: colors.grey400,
		fontFamily: fonts.Montserrat.Medium,
		fontWeight: '500',
		fontSize: HORIZONTAL_DIMENS._12,
		textDecorationLine: 'line-through',
		writingDirection: I18nManager.isRTL ? 'rtl' : 'ltr'
	},
	priceContainer: {
		flexDirection: 'column',
		alignSelf: 'center',
		justifyContent: 'center',
		alignItems: 'flex-start'
	},
	hidePrice: {
		display: 'none'
	},
	productPrice: {
		fontFamily: fonts.Montserrat.Regular,
		fontSize: HORIZONTAL_DIMENS._12,
		fontWeight: '400',
		color: colors.primary,
		textDecorationLine: 'line-through',
		textAlign: I18nManager.isRTL ? 'left' : 'auto',
		writingDirection: I18nManager.isRTL ? 'rtl' : 'ltr'
	},
	productDealPrice: {
		fontFamily: fonts.Montserrat.SemiBold,
		fontSize: HORIZONTAL_DIMENS._16,
		fontWeight: '600',
		color: colors.primary,
		textAlign: I18nManager.isRTL ? 'left' : 'auto'
		//writingDirection: I18nManager.isRTL ? 'rtl' : 'ltr'
	},
	cartActionContainer: {
		padding: HORIZONTAL_DIMENS._10,
		width: HORIZONTAL_DIMENS._111,
		alignSelf: 'flex-end',
		position: 'absolute',
		right: HORIZONTAL_DIMENS._22,
		top: VERTICAL_DIMENS._35
	},
	flexRow: {
		flexDirection: 'row',
		alignItems: 'center'
	},
	giftIcon: {
		marginRight: HORIZONTAL_DIMENS._16,
		height: VERTICAL_DIMENS._24,
		width: HORIZONTAL_DIMENS._24
	},
	giftedItemView: {
		backgroundColor: colors.white,
		flexDirection: 'row',
		alignItems: 'center',
		justifyContent: 'space-between',
		paddingVertical: VERTICAL_DIMENS._10,
		marginHorizontal: HORIZONTAL_DIMENS._16,
		borderBottomColor: colors.grey200,
		borderBottomWidth: 1
	},
	valueText: {
		color: colors.darkGray,
		fontFamily: fonts.Montserrat.SemiBold,
		fontSize: HORIZONTAL_DIMENS._16,
		fontWeight: '600',
		marginRight: HORIZONTAL_DIMENS._20
	},
	optionsMenu: {
		// paddingHorizontal: HORIZONTAL_DIMENS._21,
		right: 0
	},
	rightAction: {
		alignItems: 'center',
		flexDirection: I18nManager.isRTL ? 'row-reverse' : 'row',
		backgroundColor: colors.error,
		justifyContent: 'center',
		width: HORIZONTAL_DIMENS._80
	},
	tagContainer: {
		zIndex: 1,
		left: 0,
		position: 'absolute',
		top: VERTICAL_DIMENS._10
	}
});