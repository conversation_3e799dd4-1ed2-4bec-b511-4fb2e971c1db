/**
 * @format
 */

// if(__DEV__) {
// 	import('./ReactotronConfig').then(() => console.log('Reactotron Configured'));
// }

import { AppRegistry } from 'react-native';
import codePush from 'react-native-code-push';
import notifee from '@notifee/react-native';
import App from './App';
import { name as appName } from './app.json';

// LogBox.ignoreLogs(['Please report: ...']); // Todo: Remove this line

const codePushOptions = {
	checkFrequency: codePush.CheckFrequency.ON_APP_RESUME,
	installMode: codePush.InstallMode.ON_NEXT_RESUME
};

notifee.registerForegroundService((notification) => {
	console.log('foreground service started');
	return new Promise(() => {
		// Long running task...
	});
});

notifee.onBackgroundEvent(async ({ type, detail }) => {
	console.log('BG Notification==>', detail);
});

const MyApp = codePush(codePushOptions)(App);
AppRegistry.registerComponent(appName, () => MyApp);
