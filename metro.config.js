// const {getDefaultConfig, mergeConfig} = require('@react-native/metro-config');

// const {
//  withSentryConfig,
// } = require('@sentry/react-native/metro');

// /**
//  * Metro configuration
//  * https://reactnative.dev/docs/metro
//  *
//  * @type {import('metro-config').MetroConfig}
//  */
// const config = {};

// module.exports = withSentryConfig(mergeConfig(getDefaultConfig(__dirname), config));

const { getDefaultConfig, mergeConfig } = require('@react-native/metro-config');
const { withSentryConfig } = require('@sentry/react-native/metro');

const defaultConfig = getDefaultConfig(__dirname);

const config = {
	transformer: {
		babelTransformerPath: require.resolve('react-native-svg-transformer')
	},
	resolver: {
		assetExts: defaultConfig.resolver.assetExts.filter((ext) => ext !== 'svg'),
		sourceExts: [...defaultConfig.resolver.sourceExts, 'svg']
	}
};

module.exports = withSentryConfig(mergeConfig(defaultConfig, config));

// const { getDefaultConfig, mergeConfig } = require('@react-native/metro-config');
// const { withSentryConfig } = require('@sentry/react-native/metro');
// const { wrapWithReanimatedMetroConfig } = require('react-native-reanimated/metro-config');

// // Your custom Metro configuration
// const config = {};

// // Wrap the configuration first with Sentry, then with Reanimated
// const mergedConfig = mergeConfig(getDefaultConfig(__dirname), config);
// const configWithSentry = withSentryConfig(mergedConfig);

// module.exports = wrapWithReanimatedMetroConfig(configWithSentry);
