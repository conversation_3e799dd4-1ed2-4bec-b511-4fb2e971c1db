{"name": "hawak", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android --mode developmentDebug", "android:staging": "react-native run-android --mode stagingDebug", "android:production": "react-native run-android --mode productionDebug", "android:release": "react-native run-android --mode productionRelease", "android:generate-apk:dev": "cd android && ./gradlew assembleDevRelease", "android:generate-apk:staging": "cd android && ./gradlew assembleStagingRelease", "android:generate-apk:prod": "cd android && ./gradlew assembleRelease", "android:generate-aab:dev": "cd android && ./gradlew bundleDevRelease", "android:generate-aab:staging": "cd android && ./gradlew bundleStagingRelease", "android:generate-aab:prod": "cd android && ./gradlew bundleRelease", "ios": "react-native run-ios --scheme hawak-development", "ios:staging": "react-native run-ios --scheme hawak-staging", "ios:production": "react-native run-ios --scheme hawak", "lint": "eslint .", "start": "react-native start", "test": "jest", "postinstall": "patch-package"}, "dependencies": {"@gorhom/bottom-sheet": "^4.6.4", "@notifee/react-native": "^9.0.0", "@react-native-async-storage/async-storage": "^1.24.0", "@react-native-clipboard/clipboard": "^1.14.1", "@react-native-community/datetimepicker": "^8.2.0", "@react-native-community/netinfo": "^11.3.2", "@react-native-firebase/analytics": "^20.5.0", "@react-native-firebase/app": "^20.5.0", "@react-native-firebase/crashlytics": "^20.5.0", "@react-native-firebase/dynamic-links": "^20.5.0", "@react-native-firebase/firestore": "^20.5.0", "@react-native-firebase/messaging": "^20.5.0", "@react-native-picker/picker": "^2.8.0", "@react-native/gradle-plugin": "^0.77.0", "@react-navigation/bottom-tabs": "^6.6.1", "@react-navigation/drawer": "^6.7.2", "@react-navigation/elements": "^1.3.31", "@react-navigation/native": "^6.1.18", "@react-navigation/native-stack": "^6.11.0", "@realm/react": "^0.6.2", "@reduxjs/toolkit": "^2.2.7", "@sentry/react-native": "^6.15.0", "@sharcoux/slider": "^6.1.3", "@shopify/flash-list": "^1.7.1", "axios": "^0.28.1", "date-fns": "^3.6.0", "date-fns-tz": "^3.1.3", "date-time-format-timezone": "^1.0.22", "geolib": "^3.3.4", "i18next": "^23.15.1", "i18next-async-storage-backend2": "^2.1.0", "i18next-chained-backend": "^4.6.2", "i18next-http-backend": "^2.6.1", "intl": "^1.2.5", "libphonenumber-js": "^1.11.8", "lodash": "^4.17.21", "lottie-react-native": "^6.7.2", "react": "18.3.1", "react-i18next": "^15.0.1", "react-native": "0.75.4", "react-native-blob-util": "^0.19.11", "react-native-bootsplash": "^6.1.3", "react-native-code-push": "^8.3.1", "react-native-col": "^2.0.2", "react-native-confetti-cannon": "^1.5.2", "react-native-config": "^1.5.3", "react-native-confirmation-code-field": "^7.4.0", "react-native-device-info": "^11.1.0", "react-native-exception-handler": "^2.10.10", "react-native-fast-image": "^8.6.3", "react-native-flash-message": "^0.4.2", "react-native-fs": "^2.20.0", "react-native-geolocation-service": "^5.3.1", "react-native-gesture-handler": "^2.19.0", "react-native-get-random-values": "^1.11.0", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-linear-gradient": "^2.8.3", "react-native-maps": "^1.18.0", "react-native-maps-directions": "^1.9.0", "react-native-modal": "^13.0.1", "react-native-modal-datetime-picker": "^18.0.0", "react-native-modal-dropdown": "^1.0.2", "react-native-offline": "^6.0.2", "react-native-pager-view": "^6.4.1", "react-native-pdf": "^6.7.5", "react-native-popup-menu": "^0.16.1", "react-native-reanimated": "~3.15.1", "react-native-restart": "^0.0.27", "react-native-safe-area-context": "^4.11.0", "react-native-screens": "^3.34.0", "react-native-section-alphabet-list": "^3.0.0", "react-native-share": "^11.0.3", "react-native-shimmer-placeholder": "^2.0.9", "react-native-sound": "^0.11.2", "react-native-status-bar-height": "^2.6.0", "react-native-step-indicator": "^1.0.3", "react-native-svg": "^13.14.0", "react-native-toast-message": "^2.2.0", "react-native-virtual-keyboard": "^1.2.3", "react-native-vision-camera": "^4.5.3", "react-redux": "^9.1.2", "realm": "^12.13.1", "redux-persist": "^6.0.0", "semver": "^7.6.3", "use-between": "^1.3.5"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/babel-preset": "0.75.4", "@react-native/eslint-config": "0.75.4", "@react-native/metro-config": "0.75.4", "@react-native/typescript-config": "0.75.4", "@types/lodash": "^4.17.9", "@types/react": "^18.2.6", "@types/react-test-renderer": "^18.0.0", "@typescript-eslint/eslint-plugin": "^8.31.1", "@typescript-eslint/parser": "^8.31.1", "babel-jest": "^29.6.3", "eslint": "^8.57.1", "eslint-config-prettier": "^10.1.2", "eslint-plugin-prettier": "^5.2.6", "jest": "^29.6.3", "patch-package": "^8.0.0", "prettier": "^2.8.8", "react-native-svg-transformer": "^1.5.0", "react-test-renderer": "18.3.1", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}