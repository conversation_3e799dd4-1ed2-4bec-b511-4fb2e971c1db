/**
 *
 * Generated with the TypeScript template
 * https://github.com/react-native-community/react-native-template-typescript
 *
 * @format
 */

import 'intl';
import 'intl/locale-data/jsonp/en-US';
import 'date-time-format-timezone';
import 'react-native-gesture-handler';
import 'react-native-get-random-values';
import React, { useEffect, useRef, useState } from 'react';
import { StatusBar, LogBox, Text } from 'react-native';
import { Provider } from 'react-redux';
import Config from 'react-native-config';
import * as Sentry from '@sentry/react-native';
import { PersistGate } from 'redux-persist/integration/react';
import { NavigationContainer } from '@react-navigation/native';
import { BottomSheetModalProvider } from '@gorhom/bottom-sheet';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { MenuProvider } from 'react-native-popup-menu';
import { NetworkProvider } from 'react-native-offline';
import codePush from 'react-native-code-push';
// import SplashScreen from 'react-native-splash-screen';
import analytics from '@react-native-firebase/analytics';
import { gestureHandlerRootHOC } from 'react-native-gesture-handler';
import { setJSExceptionHandler } from 'react-native-exception-handler';
import NotificationService from './src/services/NotificationService';
import { AutoLogoutModal, MaintenanceModal } from './src/components/modals';
import MasterInit from './src/MasterInit';
import Routes from './src/navigation';
import { persistor, store } from './src/redux/store';
import Interceptor from './src/utils/Interceptor';
import './src/locales/i18n';
import { checkVersion } from './src/utils/app-update';
import BootSplash from 'react-native-bootsplash';
import { isTablet } from 'react-native-device-info';
Sentry.init({
	dsn: 'https://<EMAIL>/4505884400877568',
	environment: Config.ENVIRONMENT,
	enabled: Config.ENVIRONMENT !== 'development',
	sendDefaultPii: true
});

Interceptor.interceptor(store);

const errorHandler = (error: any) => {
	// Log error to Sentry
	Sentry.captureException(error);
	// Do any other stuff here below
};

setJSExceptionHandler(errorHandler);
// LogBox.ignoreLogs(['selector']);
const App: React.FC = () => {
	const [loading, setLoading] = useState(true);
	const routeNameRef = useRef(null);
	const navigationRef = useRef<any>();

	// Bootstrap sequence function
	async function bootstrap() {
		// Initialize notification service
		await NotificationService.initialize();

		// We'll handle initial notifications in MasterInit component
		// after the user is logged in and navigation is available
	}

	useEffect(() => {
		BootSplash.hide({ fade: true });
		bootstrap()
			.then(() => setLoading(false))
			.catch(console.error);
		codePush.notifyAppReady();
		checkVersion();
	}, []);

	if (loading) {
		return null;
	}
	// Set default properties for all Text components globally
	Text.defaultProps = Text.defaultProps || {};
	Text.defaultProps.allowFontScaling = false;
	LogBox.ignoreLogs([
		'Warning: Support for defaultProps will be removed from function components in a future major release. Use JavaScript default parameters instead.'
	]); // Ignore specific log
	return (
		<NetworkProvider pingInterval={30000}>
			<Provider store={store}>
				<PersistGate persistor={persistor}>
					<SafeAreaProvider>
						<BottomSheetModalProvider>
							<MenuProvider>
								<NavigationContainer
									ref={navigationRef}
									onReady={() => {
										if (navigationRef.current.getCurrentRoute()) {
											routeNameRef.current =
												navigationRef.current.getCurrentRoute().name;
										}
									}}
									onStateChange={async () => {
										const previousRouteName = routeNameRef.current;
										const currentRouteName =
											navigationRef.current?.getCurrentRoute()?.name;

										if (previousRouteName !== currentRouteName) {
											await analytics().logScreenView({
												screen_name: currentRouteName,
												screen_class: currentRouteName
											});
										}
										routeNameRef.current = currentRouteName;
									}}
								>
									<StatusBar
										barStyle={isTablet() ? 'light-content' : 'dark-content'}
										translucent={true}
										backgroundColor="transparent"
									/>
									<MasterInit>
										<Routes />
									</MasterInit>
									<MaintenanceModal />
									<AutoLogoutModal />
								</NavigationContainer>
							</MenuProvider>
						</BottomSheetModalProvider>
					</SafeAreaProvider>
				</PersistGate>
			</Provider>
		</NetworkProvider>
	);
};

const RNGHRootHOC = gestureHandlerRootHOC(App);

export default Sentry.wrap(RNGHRootHOC);
